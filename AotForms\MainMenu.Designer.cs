﻿namespace AotForms
{
    partial class MainMenu
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            guna2CustomCheckBox1 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label1 = new Label();
            label2 = new Label();
            guna2CustomCheckBox2 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2PictureBox3 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
            guna2DragControl1 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl2 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2Button8 = new Guna.UI2.WinForms.Guna2Button();
            guna2DragControl3 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl4 = new Guna.UI2.WinForms.Guna2DragControl(components);
            label3 = new Label();
            guna2CustomCheckBox3 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel3 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Button9 = new Guna.UI2.WinForms.Guna2Button();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).BeginInit();
            guna2Panel3.SuspendLayout();
            SuspendLayout();
            // 
            // guna2Button1
            // 
            guna2Button1.Animated = true;
            guna2Button1.BorderColor = Color.FromArgb(64, 64, 64);
            guna2Button1.BorderRadius = 5;
            guna2Button1.BorderThickness = 1;
            guna2Button1.CustomizableEdges = customizableEdges1;
            guna2Button1.DisabledState.BorderColor = Color.DarkGray;
            guna2Button1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button1.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button1.FillColor = Color.FromArgb(14, 14, 14);
            guna2Button1.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button1.ForeColor = Color.Silver;
            guna2Button1.Location = new Point(13, 5);
            guna2Button1.Name = "guna2Button1";
            guna2Button1.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2Button1.Size = new Size(265, 45);
            guna2Button1.TabIndex = 0;
            guna2Button1.Text = "┋                     Start Cheat Lobby";
            guna2Button1.TextAlign = HorizontalAlignment.Left;
            guna2Button1.Click += guna2Button1_Click;
            // 
            // guna2BorderlessForm1
            // 
            guna2BorderlessForm1.BorderRadius = 20;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            guna2BorderlessForm1.HasFormShadow = false;
            guna2BorderlessForm1.ResizeForm = false;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // guna2Button3
            // 
            guna2Button3.Animated = true;
            guna2Button3.BorderColor = Color.FromArgb(64, 64, 64);
            guna2Button3.BorderRadius = 5;
            guna2Button3.BorderThickness = 1;
            guna2Button3.CustomizableEdges = customizableEdges19;
            guna2Button3.DisabledState.BorderColor = Color.DarkGray;
            guna2Button3.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button3.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button3.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button3.FillColor = Color.FromArgb(14, 14, 14);
            guna2Button3.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button3.ForeColor = Color.Silver;
            guna2Button3.Location = new Point(12, 58);
            guna2Button3.Name = "guna2Button3";
            guna2Button3.ShadowDecoration.CustomizableEdges = customizableEdges20;
            guna2Button3.Size = new Size(265, 45);
            guna2Button3.TabIndex = 3;
            guna2Button3.Text = "┋                       ESP VISUALS";
            guna2Button3.TextAlign = HorizontalAlignment.Left;
            guna2Button3.Click += guna2Button3_Click;
            // 
            // guna2Button4
            // 
            guna2Button4.Animated = true;
            guna2Button4.BorderColor = Color.FromArgb(64, 64, 64);
            guna2Button4.BorderRadius = 5;
            guna2Button4.BorderThickness = 1;
            guna2Button4.CustomizableEdges = customizableEdges17;
            guna2Button4.DisabledState.BorderColor = Color.DarkGray;
            guna2Button4.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button4.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button4.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button4.FillColor = Color.FromArgb(14, 14, 14);
            guna2Button4.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button4.ForeColor = Color.Silver;
            guna2Button4.Location = new Point(12, 109);
            guna2Button4.Name = "guna2Button4";
            guna2Button4.ShadowDecoration.CustomizableEdges = customizableEdges18;
            guna2Button4.Size = new Size(265, 45);
            guna2Button4.TabIndex = 4;
            guna2Button4.Text = "┋                    CONFIGURATION";
            guna2Button4.TextAlign = HorizontalAlignment.Left;
            guna2Button4.Click += guna2Button4_Click;
            // 
            // guna2CustomCheckBox1
            // 
            guna2CustomCheckBox1.Animated = true;
            guna2CustomCheckBox1.BackColor = Color.FromArgb(15, 15, 15);
            guna2CustomCheckBox1.CheckedState.BorderColor = Color.Cyan;
            guna2CustomCheckBox1.CheckedState.BorderRadius = 5;
            guna2CustomCheckBox1.CheckedState.BorderThickness = 1;
            guna2CustomCheckBox1.CheckedState.FillColor = Color.Cyan;
            guna2CustomCheckBox1.CheckMarkColor = Color.Silver;
            guna2CustomCheckBox1.CustomizableEdges = customizableEdges5;
            guna2CustomCheckBox1.Location = new Point(-13, 2);
            guna2CustomCheckBox1.Name = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.ShadowDecoration.CustomizableEdges = customizableEdges6;
            guna2CustomCheckBox1.Size = new Size(23, 23);
            guna2CustomCheckBox1.TabIndex = 6;
            guna2CustomCheckBox1.Text = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.UncheckedState.BorderColor = Color.FromArgb(64, 64, 64);
            guna2CustomCheckBox1.UncheckedState.BorderRadius = 5;
            guna2CustomCheckBox1.UncheckedState.BorderThickness = 1;
            guna2CustomCheckBox1.UncheckedState.FillColor = Color.FromArgb(14, 14, 14);
            guna2CustomCheckBox1.Click += guna2CustomCheckBox1_Click_1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.BackColor = Color.Transparent;
            label1.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label1.ForeColor = Color.Silver;
            label1.Location = new Point(34, 5);
            label1.Name = "label1";
            label1.Size = new Size(98, 15);
            label1.TabIndex = 7;
            label1.Text = "STREAM MODE";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.BackColor = Color.Transparent;
            label2.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label2.ForeColor = Color.Silver;
            label2.Location = new Point(34, 35);
            label2.Name = "label2";
            label2.Size = new Size(149, 15);
            label2.TabIndex = 9;
            label2.Text = "DISABLE SOUND EFFECT";
            // 
            // guna2CustomCheckBox2
            // 
            guna2CustomCheckBox2.Animated = true;
            guna2CustomCheckBox2.BackColor = Color.FromArgb(15, 15, 15);
            guna2CustomCheckBox2.CheckedState.BorderColor = Color.Cyan;
            guna2CustomCheckBox2.CheckedState.BorderRadius = 5;
            guna2CustomCheckBox2.CheckedState.BorderThickness = 1;
            guna2CustomCheckBox2.CheckedState.FillColor = Color.Cyan;
            guna2CustomCheckBox2.CheckMarkColor = Color.Silver;
            guna2CustomCheckBox2.CustomizableEdges = customizableEdges9;
            guna2CustomCheckBox2.Location = new Point(6, 31);
            guna2CustomCheckBox2.Name = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.ShadowDecoration.CustomizableEdges = customizableEdges10;
            guna2CustomCheckBox2.Size = new Size(23, 23);
            guna2CustomCheckBox2.TabIndex = 8;
            guna2CustomCheckBox2.Text = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.UncheckedState.BorderColor = Color.FromArgb(64, 64, 64);
            guna2CustomCheckBox2.UncheckedState.BorderRadius = 5;
            guna2CustomCheckBox2.UncheckedState.BorderThickness = 1;
            guna2CustomCheckBox2.UncheckedState.FillColor = Color.FromArgb(14, 14, 14);
            guna2CustomCheckBox2.Click += guna2CustomCheckBox2_Click;
            // 
            // guna2PictureBox3
            // 
            guna2PictureBox3.AutoRoundedCorners = true;
            guna2PictureBox3.BackColor = Color.FromArgb(14, 14, 14);
            guna2PictureBox3.BorderRadius = 3;
            guna2PictureBox3.CustomizableEdges = customizableEdges15;
            guna2PictureBox3.FillColor = Color.Gray;
            guna2PictureBox3.ImageRotate = 0F;
            guna2PictureBox3.Location = new Point(262, 12);
            guna2PictureBox3.Name = "guna2PictureBox3";
            guna2PictureBox3.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2PictureBox3.Size = new Size(8, 8);
            guna2PictureBox3.TabIndex = 10;
            guna2PictureBox3.TabStop = false;
            // 
            // guna2Button5
            // 
            guna2Button5.Animated = true;
            guna2Button5.BackColor = Color.FromArgb(17, 17, 17);
            guna2Button5.BorderRadius = 3;
            guna2Button5.CustomizableEdges = customizableEdges11;
            guna2Button5.DisabledState.BorderColor = Color.DarkGray;
            guna2Button5.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button5.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button5.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button5.FillColor = Color.Yellow;
            guna2Button5.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button5.ForeColor = Color.White;
            guna2Button5.Image = Client.Properties.Resources.arrow;
            guna2Button5.ImageAlign = HorizontalAlignment.Left;
            guna2Button5.ImageSize = new Size(15, 15);
            guna2Button5.Location = new Point(6, 114);
            guna2Button5.Name = "guna2Button5";
            guna2Button5.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2Button5.Size = new Size(252, 21);
            guna2Button5.TabIndex = 13;
            guna2Button5.Text = "REFRESH";
            guna2Button5.Click += guna2Button5_Click;
            // 
            // guna2DragControl1
            // 
            guna2DragControl1.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl1.TransparentWhileDrag = false;
            // 
            // guna2DragControl2
            // 
            guna2DragControl2.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl2.TransparentWhileDrag = false;
            // 
            // guna2Button8
            // 
            guna2Button8.Animated = true;
            guna2Button8.BackColor = Color.FromArgb(17, 17, 17);
            guna2Button8.BorderRadius = 3;
            guna2Button8.CustomizableEdges = customizableEdges3;
            guna2Button8.DisabledState.BorderColor = Color.DarkGray;
            guna2Button8.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button8.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button8.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button8.FillColor = Color.Cyan;
            guna2Button8.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button8.ForeColor = Color.White;
            guna2Button8.Image = Client.Properties.Resources.human_skull;
            guna2Button8.ImageAlign = HorizontalAlignment.Left;
            guna2Button8.ImageSize = new Size(15, 15);
            guna2Button8.Location = new Point(6, 89);
            guna2Button8.Name = "guna2Button8";
            guna2Button8.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2Button8.Size = new Size(252, 21);
            guna2Button8.TabIndex = 16;
            guna2Button8.Text = "KILL";
            guna2Button8.Click += guna2Button8_Click;
            // 
            // guna2DragControl3
            // 
            guna2DragControl3.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl3.UseTransparentDrag = true;
            // 
            // guna2DragControl4
            // 
            guna2DragControl4.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl4.UseTransparentDrag = true;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.BackColor = Color.Transparent;
            label3.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label3.ForeColor = Color.Silver;
            label3.Location = new Point(34, 62);
            label3.Name = "label3";
            label3.Size = new Size(68, 15);
            label3.TabIndex = 18;
            label3.Text = "MINIMIZE";
            // 
            // guna2CustomCheckBox3
            // 
            guna2CustomCheckBox3.Animated = true;
            guna2CustomCheckBox3.BackColor = Color.FromArgb(15, 15, 15);
            guna2CustomCheckBox3.CheckedState.BorderColor = Color.Cyan;
            guna2CustomCheckBox3.CheckedState.BorderRadius = 5;
            guna2CustomCheckBox3.CheckedState.BorderThickness = 1;
            guna2CustomCheckBox3.CheckedState.FillColor = Color.Cyan;
            guna2CustomCheckBox3.CheckMarkColor = Color.Silver;
            guna2CustomCheckBox3.CustomizableEdges = customizableEdges7;
            guna2CustomCheckBox3.Location = new Point(6, 59);
            guna2CustomCheckBox3.Name = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2CustomCheckBox3.Size = new Size(23, 23);
            guna2CustomCheckBox3.TabIndex = 17;
            guna2CustomCheckBox3.Text = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.UncheckedState.BorderColor = Color.FromArgb(64, 64, 64);
            guna2CustomCheckBox3.UncheckedState.BorderRadius = 5;
            guna2CustomCheckBox3.UncheckedState.BorderThickness = 1;
            guna2CustomCheckBox3.UncheckedState.FillColor = Color.FromArgb(14, 14, 14);
            guna2CustomCheckBox3.Click += guna2CustomCheckBox3_Click;
            // 
            // guna2Panel3
            // 
            guna2Panel3.Controls.Add(guna2Button8);
            guna2Panel3.Controls.Add(label3);
            guna2Panel3.Controls.Add(guna2CustomCheckBox1);
            guna2Panel3.Controls.Add(guna2CustomCheckBox3);
            guna2Panel3.Controls.Add(label1);
            guna2Panel3.Controls.Add(guna2CustomCheckBox2);
            guna2Panel3.Controls.Add(label2);
            guna2Panel3.Controls.Add(guna2Button5);
            guna2Panel3.CustomizableEdges = customizableEdges13;
            guna2Panel3.FillColor = Color.FromArgb(25, 25, 25);
            guna2Panel3.Location = new Point(12, 265);
            guna2Panel3.Margin = new Padding(2);
            guna2Panel3.Name = "guna2Panel3";
            guna2Panel3.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2Panel3.Size = new Size(265, 142);
            guna2Panel3.TabIndex = 19;
            // 
            // guna2Button9
            // 
            guna2Button9.Animated = true;
            guna2Button9.BorderColor = Color.FromArgb(64, 64, 64);
            guna2Button9.BorderRadius = 5;
            guna2Button9.BorderThickness = 1;
            guna2Button9.CustomizableEdges = customizableEdges21;
            guna2Button9.DisabledState.BorderColor = Color.DarkGray;
            guna2Button9.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button9.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button9.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button9.FillColor = Color.FromArgb(14, 14, 14);
            guna2Button9.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            guna2Button9.ForeColor = Color.Silver;
            guna2Button9.Location = new Point(13, 160);
            guna2Button9.Name = "guna2Button9";
            guna2Button9.ShadowDecoration.CustomizableEdges = customizableEdges22;
            guna2Button9.Size = new Size(265, 45);
            guna2Button9.TabIndex = 78;
            guna2Button9.Text = "┋                    MENU AIMBOT";
            guna2Button9.TextAlign = HorizontalAlignment.Left;
            guna2Button9.Click += guna2Button9_Click;
            // 
            // MainMenu
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(8, 8, 8);
            BackgroundImageLayout = ImageLayout.Stretch;
            ClientSize = new Size(290, 55);
            Controls.Add(guna2Panel3);
            Controls.Add(guna2PictureBox3);
            Controls.Add(guna2Button4);
            Controls.Add(guna2Button3);
            Controls.Add(guna2Button1);
            Controls.Add(guna2Button9);
            DoubleBuffered = true;
            FormBorderStyle = FormBorderStyle.None;
            Name = "MainMenu";
            ShowIcon = false;
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "OUSSAMA CHEAT";
            Load += Form2_Load;
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).EndInit();
            guna2Panel3.ResumeLayout(false);
            guna2Panel3.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private Guna.UI2.WinForms.Guna2Button guna2Button1;
        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private Guna.UI2.WinForms.Guna2Button guna2Button4;
        private Guna.UI2.WinForms.Guna2Button guna2Button3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox1;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox3;
        private Label label2;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox2;
        private Label label1;
        private TreeView dataTreeView;
        private Guna.UI2.WinForms.Guna2Button guna2Button5;
        private Label label6;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl1;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl2;
        private Guna.UI2.WinForms.Guna2Button guna2Button8;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl3;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl4;
        private Label label3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox3;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel3;
        private Guna.UI2.WinForms.Guna2Button guna2Button9;
    }
}