{"runtimeOptions": {"tfm": "net7.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "7.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "7.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"System.Resources.ResourceManager.AllowCustomResourceTypes": true, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": true, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": true, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}