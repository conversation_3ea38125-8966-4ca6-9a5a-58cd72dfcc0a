﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Drawing.Drawing2D;

namespace CustomControls
{
    [ToolboxItem(true)]
    [Description("زر كبير مع نص في الوسط وصورة وشفافية وحدود رمادية وثلاث دوائر بيضاء")]
    public class Buttonred : UserControl
    {
        private bool isChecked = false;
        private Color checkedColor = Color.Green;
        private Color uncheckedColor = Color.Red;
        private Color buttonColor = Color.FromArgb(100, 39, 39, 39);
        private Color borderColor = Color.Gray;
        private string buttonText = "زر";
        private Image buttonImage = null;
        private Font buttonFont = new Font("Segoe UI", 10f, FontStyle.Bold);
        private int borderWidth = 1;
        private int borderRadius = 8;
        private bool isPressed = false;
        private bool isHovered = false;
        private Point clickPosition;
        private float glowOpacity = 1.0f;
        private System.Windows.Forms.Timer glowTimer;
        private bool hasBeenClicked = false;

        public event EventHandler CheckedChanged;

        public Buttonred()
        {
            InitializeComponent();
            SetStyle(ControlStyles.SupportsTransparentBackColor |
                     ControlStyles.OptimizedDoubleBuffer |
                     ControlStyles.UserPaint |
                     ControlStyles.AllPaintingInWmPaint, true);
            this.BackColor = Color.Transparent;

            glowTimer = new System.Windows.Forms.Timer();
            glowTimer.Interval = 15; // 15 مللي ثانية
            glowTimer.Tick += GlowTimer_Tick;
        }

        private void InitializeComponent()
        {
            SuspendLayout();
            // 
            // Buttonred
            // 
            Cursor = Cursors.Hand;
            Name = "Buttonred";
            Size = new Size(150, 50);
            Load += Buttonred_Load;
            Click += Buttonred_Click;
            MouseDown += Buttonred_MouseDown;
            MouseEnter += Buttonred_MouseEnter;
            MouseLeave += Buttonred_MouseLeave;
            MouseUp += Buttonred_MouseUp;
            ResumeLayout(false);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الحدود الرمادية (مرتين للحصول على مظهر أكثر احترافية)
            using (var path = new GraphicsPath())
            {
                path.AddRoundedRectangle(this.ClientRectangle, borderRadius);
                using (var pen = new Pen(borderColor, borderWidth))
                {
                    e.Graphics.DrawPath(pen, path);
                }
                using (var pen = new Pen(Color.FromArgb(100, borderColor), borderWidth))
                {
                    e.Graphics.DrawPath(pen, path);
                }
            }

            // رسم الزر الرئيسي بشفافية محسنة
            Rectangle innerRect = new Rectangle(
                borderWidth,
                borderWidth,
                this.Width - 2 * borderWidth,
                this.Height - 2 * borderWidth
            );
            using (var path = new GraphicsPath())
            {
                path.AddRoundedRectangle(innerRect, borderRadius - borderWidth);
                using (var brush = new SolidBrush(Color.FromArgb(200, GetButtonColor())))
                {
                    e.Graphics.FillPath(brush, path);
                }

                // إضافة تأثير التوهج المحسن بحجم أصغر ولون رمادي داكن
                if ((isPressed || glowOpacity > 0) && hasBeenClicked)
                {
                    using (var glowPath = new GraphicsPath())
                    {
                        int glowSize = Math.Min(Width, Height) / 1; // تقليل حجم التوهج
                        glowPath.AddEllipse(clickPosition.X - glowSize / 2, clickPosition.Y - glowSize / 2, glowSize, glowSize);

                        using (var glowBrush = new PathGradientBrush(glowPath))
                        {
                            glowBrush.CenterPoint = clickPosition;
                            glowBrush.CenterColor = Color.FromArgb((int)(100 * glowOpacity), Color.FromArgb(203, 86, 187));
                            glowBrush.SurroundColors = new Color[] { Color.Transparent };

                            e.Graphics.FillPath(glowBrush, glowPath);
                        }

                        // إضافة تأثير إضافي للتوهج باستخدام LinearGradientBrush
                        using (var linearGlow = new LinearGradientBrush(
                            new Point(clickPosition.X - glowSize / 4, clickPosition.Y - glowSize / 4),
                            new Point(clickPosition.X + glowSize / 4, clickPosition.Y + glowSize / 4),
                            Color.FromArgb((int)(80 * glowOpacity), Color.FromArgb(19, 19, 19)),
                            Color.Transparent))
                        {
                            e.Graphics.FillEllipse(linearGlow,
                                clickPosition.X - glowSize / 4,
                                clickPosition.Y - glowSize / 4,
                                glowSize / 2,
                                glowSize / 2);
                        }
                    }
                }
            }

            // رسم الدائرة الداخلية (الحالة) في الأعلى على اليمين
            int stateCircleSize = 7;
            int stateCircleX = this.Width - stateCircleSize - 5 - borderWidth;
            int stateCircleY = 5 + borderWidth;
            using (var brush = new SolidBrush(isChecked ? checkedColor : uncheckedColor))
            {
                e.Graphics.FillEllipse(brush, stateCircleX, stateCircleY, stateCircleSize, stateCircleSize);
            }

            // رسم الدوائر البيضاء الثلاث
            int whiteCircleSize = 3;
            int whiteCircleX = 15 + borderWidth;
            int whiteCircleSpacing = 2;
            int totalWhiteCirclesHeight = (3 * whiteCircleSize) + (2 * whiteCircleSpacing);
            int whiteCircleStartY = (this.Height - totalWhiteCirclesHeight) / 2;

            using (var brush = new SolidBrush(Color.White))
            {
                for (int i = 0; i < 3; i++)
                {
                    int whiteCircleY = whiteCircleStartY + (i * (whiteCircleSize + whiteCircleSpacing));
                    e.Graphics.FillEllipse(brush, whiteCircleX, whiteCircleY, whiteCircleSize, whiteCircleSize);
                }
            }

            // رسم النص في الوسط
            using (var brush = new SolidBrush(Color.Gray))
            {
                var textRect = new RectangleF(borderWidth, borderWidth, this.Width - 2 * borderWidth, this.Height - 2 * borderWidth);
                e.Graphics.DrawString(buttonText, buttonFont, brush, textRect, new StringFormat() { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
            }

            // رسم الصورة إذا كانت موجودة
            if (buttonImage != null)
            {
                int imageSize = this.Height - 20 - 2 * borderWidth;
                int imageX = this.Width - imageSize - 10 - borderWidth;
                int imageY = (this.Height - imageSize) / 2;
                e.Graphics.DrawImage(buttonImage, imageX, imageY, imageSize, imageSize);
            }
        }

        private Color GetButtonColor()
        {
            if (isPressed)
            {
                return Color.FromArgb(220, buttonColor);
            }
            else if (isHovered)
            {
                return Color.FromArgb(220, 17, 18, 25);   // تغيير لون الزر إلى الأبيض عند المرور عليه
            }
            else
            {
                return buttonColor;
            }
        }

        private void Buttonred_Click(object sender, EventArgs e)
        {
            isChecked = !isChecked;
            this.Invalidate();
            OnCheckedChanged(EventArgs.Empty);
        }

        private void Buttonred_MouseDown(object sender, MouseEventArgs e)
        {
            isPressed = true;
            clickPosition = e.Location;
            glowOpacity = 1.0f;
            glowTimer.Stop();
            hasBeenClicked = true;
            this.Invalidate();
        }

        private void Buttonred_MouseUp(object sender, MouseEventArgs e)
        {
            isPressed = false;
            glowTimer.Start();
            this.Invalidate();
        }

        private void Buttonred_MouseEnter(object sender, EventArgs e)
        {
            isHovered = true;
            this.Invalidate();
        }

        private void Buttonred_MouseLeave(object sender, EventArgs e)
        {
            isHovered = false;
            this.Invalidate();
        }

        private void GlowTimer_Tick(object sender, EventArgs e)
        {
            glowOpacity -= 0.05f;
            if (glowOpacity <= 0)
            {
                glowOpacity = 0;
                glowTimer.Stop();
            }
            this.Invalidate();
        }

        protected virtual void OnCheckedChanged(EventArgs e)
        {
            CheckedChanged?.Invoke(this, e);
        }

        private void Buttonred_Load(object sender, EventArgs e)
        {

        }

        // الخصائص

        [Category("Appearance")]
        [Description("نص الزر")]
        public string ButtonText
        {
            get { return buttonText; }
            set
            {
                buttonText = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("سمك حدود الزر")]
        public int BorderWidth
        {
            get { return borderWidth; }
            set
            {
                borderWidth = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("نصف قطر حدود الزر")]
        public int BorderRadius
        {
            get { return borderRadius; }
            set
            {
                borderRadius = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("لون الزر")]
        public Color ButtonColor
        {
            get { return buttonColor; }
            set
            {
                buttonColor = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("لون الحدود")]
        public Color BorderColor
        {
            get { return borderColor; }
            set
            {
                borderColor = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("صورة الزر")]
        public Image ButtonImage
        {
            get { return buttonImage; }
            set
            {
                buttonImage = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("خط الزر")]
        public Font ButtonFont
        {
            get { return buttonFont; }
            set
            {
                buttonFont = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("حالة التحديد")]
        public bool Checked
        {
            get { return isChecked; }
            set
            {
                if (isChecked != value)
                {
                    isChecked = value;
                    OnCheckedChanged(EventArgs.Empty);
                    this.Invalidate();
                }
            }
        }

        [Category("Appearance")]
        [Description("لون الحالة المحددة")]
        public Color CheckedColor
        {
            get { return checkedColor; }
            set
            {
                checkedColor = value;
                this.Invalidate();
            }
        }

        [Category("Appearance")]
        [Description("لون الحالة غير المحددة")]
        public Color UncheckedColor
        {
            get { return uncheckedColor; }
            set
            {
                uncheckedColor = value;
                this.Invalidate();
            }
        }
    }

    public static class GraphicsExtensions
    {
        public static void AddRoundedRectangle(this GraphicsPath path, Rectangle bounds, int radius)
        {
            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(bounds.Location, size);

            path.AddArc(arc, 180, 90);
            arc.X = bounds.Right - diameter;
            path.AddArc(arc, 270, 90);
            arc.Y = bounds.Bottom - diameter;
            path.AddArc(arc, 0, 90);
            arc.X = bounds.Left;
            path.AddArc(arc, 90, 90);
            path.CloseFigure();
        }
    }
}