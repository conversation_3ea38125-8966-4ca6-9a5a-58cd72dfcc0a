﻿using System;
using System.Numerics;

namespace AotForms
{
    internal static class Aimkill
    {
        internal static void Work()
        {
            while (true)
            {
                // Check if AimKill is enabled in Config
                if (!Config.AIKILL)
                {
                    continue;
                }

                Entity target = null;
                float closestDistance = float.MaxValue;

                // Ensure Core dimensions and matrix are valid
                if (Core.Width == -1 || Core.Height == -1 || !Core.HaveMatrix)
                {
                    continue;
                }

                var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

                foreach (var entity in Core.Entities.Values)
                {
                    // Skip invalid targets
                    if (!entity.IsKnown || entity.IsDead || (Config.IgnoreKnocked && entity.IsKnocked))
                        continue;

                    var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);

                    if (head2D == Vector2.Zero)
                        continue; // Ensure head is within screen bounds

                    var x = head2D.X - screenCenter.X;
                    var y = head2D.Y - screenCenter.Y;
                    var crosshairDist = (float)Math.Sqrt(x * x + y * y);

                    // Update target if it's closer
                    if (crosshairDist < closestDistance)
                    {
                        closestDistance = crosshairDist;
                        target = entity;
                    }
                }

                //if (target != null)
                //{
                //    // Check if the player has a weapon in hand
                //    if (HasWeaponInHand((uint)(Core.LocalPlayer & 0xFFFFFFFF)))
                //    {
                //        // Direct kill logic using InitBase2 offsets
                //        if (!target.IsDead && target.Health <= 0)
                //        {
                //            InternalMemory.Write(Core.LocalPlayer + Offsets.Player_Data_AimKill, 1);
                //        }
                //    }
                //}
            }
        }

        /// <summary>
        /// Checks if the local player has a weapon in hand using InitBase2 offsets.
        /// </summary>
        private static bool HasWeaponInHand(uint localPlayer)
        {
            uint weaponDataOffset = Offsets.WeaponData;
            uint weaponData = 0;

            // Read weapon data from memory using InitBase2
            if (InternalMemory.Read(localPlayer + Offsets.WeaponData, out weaponData))
            {
                return weaponData != 0;
            }

            return false;
        }
    }
}
