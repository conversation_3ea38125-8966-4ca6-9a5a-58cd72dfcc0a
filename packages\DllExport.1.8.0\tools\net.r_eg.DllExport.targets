﻿<?xml version="1.0" encoding="utf-8"?><!-- https://github.com/3F/DllExport -->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <DllExportModImported>true</DllExportModImported>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Identify via TargetFramework because TargetFrameworkIdentifier is not ready at this stage and MSBuild 16+ -->
    <_tfmlower>$(TargetFramework.ToLower())</_tfmlower>
    <IsNetCoreBased Condition="$(_tfmlower.StartsWith('netc')) Or $(_tfmlower.StartsWith('nets')) Or ('$(_tfmlower)'!='' And $([System.Char]::IsDigit($(_tfmlower[3]))) And '$(_tfmlower[3])' &gt;= '5')">true</IsNetCoreBased>
  </PropertyGroup>

  <PropertyGroup Label="Bitwise values">
    <DllExportPreProcType Condition="'$(DllExportPreProcType)'==''">0</DllExportPreProcType>
    <DllExportTypeRefOptions Condition="'$(DllExportTypeRefOptions)'==''">0</DllExportTypeRefOptions>
  </PropertyGroup>

  <PropertyGroup Condition="'$(IsNetCoreBased)'=='true' And '$(DllExportPreProcType)'!='0'">
    <CopyLocalLockFileAssemblies Condition="'$([MSBuild]::BitwiseAnd($(DllExportPreProcType), 2))'=='2' And ('$(NoForceCopyLocalLockFileAssemblies)'!='true')">true</CopyLocalLockFileAssemblies> <!-- PreProc: CmdType.Conari = 0x2, -->
    <DebugType Condition="$([MSBuild]::BitwiseAnd($(DllExportPreProcType), 8))==8 And ('$(DebugType)'!='full' And '$(DebugType)'!='pdbonly')">pdbonly</DebugType> <!-- CmdType.DebugInfo = 0x8, -->
  </PropertyGroup>

  <PropertyGroup>
    <DllExportDirMetaCore Condition="$(DllExportDirMetaCore) == ''">metacor</DllExportDirMetaCore>
    <DllExportDirMetaFX Condition="$(DllExportDirMetaFX) == ''">metalib</DllExportDirMetaFX>
  </PropertyGroup>

  <!-- TODO: metalib / metacor  -->
  <PropertyGroup>
    <DllExportMetaXBase Condition="'$(IsNetCoreBased)'=='true'">$(DllExportDirMetaCore)</DllExportMetaXBase>
    <DllExportMetaXBase Condition="'$(IsNetCoreBased)'!='true'">$(DllExportDirMetaFX)</DllExportMetaXBase>
    <DllExportLibPath Condition="'$(DllExportLibPath)'==''">gcache\$(DllExportMetaXBase)\$(DllExportNamespace)\</DllExportLibPath>
  </PropertyGroup>

  <PropertyGroup>
    <DllExportRootPkg Condition="'$(DllExportRootPkg)'==''">$(MSBuildThisFileDirectory)..\</DllExportRootPkg>
    <DllExportVSRoot Condition="'$(DllExportVSRoot)'=='' And '$(VsInstallRoot)'!=''">$(VsInstallRoot)\</DllExportVSRoot>
    <DllExportVSRoot Condition="'$(DllExportVSRoot)'==''">$(DevEnvDir)\..\..\</DllExportVSRoot>
    <DllExportVSBin Condition="'$(DllExportVSBin)'==''">$(DllExportVSRoot)Common7\IDE\</DllExportVSBin>

    <DllExportMetaXBase Condition="'$(DllExportMetaXBase)'==''">metalib</DllExportMetaXBase>
    <DllExportLibPath Condition="'$(DllExportLibPath)'==''">gcache\$(DllExportMetaXBase)\$(DllExportNamespace)\</DllExportLibPath>
    <DllExportToolsPath Condition="'$(DllExportToolsPath)'==''">tools\</DllExportToolsPath>

    <DllExportNamespace Condition="'$(DllExportNamespace)'==''"></DllExportNamespace>
    <DllExportLibFullPath Condition="'$(DllExportLibFullPath)'==''">$(DllExportRootPkg)$(DllExportLibPath)</DllExportLibFullPath>
    <DllExportMetaLibAttr Condition="'$(DllExportMetaLibAttr)'==''">DllExportAttribute</DllExportMetaLibAttr>
    <DllExportMetaLibName Condition="'$(DllExportMetaLibName)'==''">DllExport.dll</DllExportMetaLibName>
    <DllExportMetaLibFullPath Condition="'$(DllExportMetaLibFullPath)'==''">$(DllExportLibFullPath)$(DllExportMetaLibName)</DllExportMetaLibFullPath>
    <DllExportOurILAsmPath Condition="'$(DllExportOurILAsmPath)'==''">$(DllExportRootPkg)$(DllExportToolsPath)coreclr\</DllExportOurILAsmPath>
    <DllExportILAsmCustomPath Condition="'$(DllExportILAsmCustomPath)'==''"></DllExportILAsmCustomPath>

    <DllExportOptClrTypesPath Condition="'$(DllExportOptClrTypesPath)'==''">$(DllExportRootPkg)$(DllExportToolsPath)clrtypes\</DllExportOptClrTypesPath>

    <DllExportAttributeFullName Condition="'$(DllExportNamespace)'!=''">$(DllExportNamespace).$(DllExportMetaLibAttr)</DllExportAttributeFullName>
    <DllExportAttributeFullName Condition="'$(DllExportNamespace)'==''">$(DllExportMetaLibAttr)</DllExportAttributeFullName>

    <DllExportDefPlatform>$(PlatformTarget.ToLower())</DllExportDefPlatform>
    <DllExportDefPlatform Condition="'$(DllExportDefPlatform)'==''">anycpu</DllExportDefPlatform>

  </PropertyGroup>

  <ItemGroup Condition="'$(DllExportTypeRefOptions)'!='' Or '$(DllExportTypeRefOptions)'&gt;'0'">
    <Compile Condition="'$([MSBuild]::BitwiseAnd($(DllExportTypeRefOptions), 1))'=='1'" Include="$(DllExportOptClrTypesPath)DefaultInterpolatedStringHandler\.cs" Visible="false" InProject="false" />
  </ItemGroup>

  <PropertyGroup>
    <DllExportCopyToPublishDirectoryType Condition="'$(DllExportCopyToPublishDirectoryType)'==''">PreserveNewest</DllExportCopyToPublishDirectoryType>
  </PropertyGroup>

  <Target Condition="'$(DllExportResolvePublish)'!='false' and '$(DllExportDefPlatform)'=='anycpu'"
          Name="DllExportComputeResolvedFilesToPublishList"
          BeforeTargets="ComputeResolvedFilesToPublishList">
      <ItemGroup>
        <!-- Regarding my old solution from https://github.com/3F/DllExport/issues/224#issuecomment-1546883382
             either wildcards (*) is only ok for Visual Studio but problematic when building directly in MSBuild (empty Include) because yeah at that stage paths are not ready to be resolved;
             or requires ::GetParent() workaround to use inside Targets like this.
        -->
        <DllExportFilesToPublish Include="$(TargetDir)x86\*" PlatformDir="x86\" />
        <DllExportFilesToPublish Include="$(TargetDir)x64\*" PlatformDir="x64\" />
        <ResolvedFileToPublish Include="@(DllExportFilesToPublish)" RelativePath="%(PlatformDir)%(Filename)%(Extension)"
                               CopyToPublishDirectory="$(DllExportCopyToPublishDirectoryType)" />
      </ItemGroup>
  </Target>

  <!-- F-262, for a single platform -->
  <ItemGroup Condition="'$(DllExportResolvePublish)'!='false' And '$(DllExportDefPlatform)'!='anycpu' And '$(DllExportResolvePublishForSingle)'!='false'">
    <ResolvedFileToPublish Include="$(TargetPath)" RelativePath="%(Filename)%(Extension)"
                           CopyToPublishDirectory="$(DllExportCopyToPublishDirectoryType)" />

    <ResolvedFileToPublish Include="$(TargetDir)$(TargetName).pdb" RelativePath="%(Filename)%(Extension)"
                           CopyToPublishDirectory="$(DllExportCopyToPublishDirectoryType)" />
  </ItemGroup>
  <Target Condition="'$(DllExportResolvePublish)'!='false' And '$(DllExportResolvePublishForSingle)'!='false'"
          Name="DllExportComputeResolvedFilesToPublishListFallback"
          BeforeTargets="ComputeResolvedFilesToPublishList"
          AfterTargets="DllExportComputeResolvedFilesToPublishList">

    <PropertyGroup>
      <CopyBuildOutputToPublishDirectory>false</CopyBuildOutputToPublishDirectory>
      <CopyOutputSymbolsToPublishDirectory>false</CopyOutputSymbolsToPublishDirectory>
    </PropertyGroup>

    <!-- TODO: tl;dr @(IntermediateAssembly) includes the build product (.dll or .exe); so... do we have other cases where this is really necessary? -->
    <ItemGroup Condition="'$(DllExportFallbackDontCopyBuildOutputToPublishDirectory)'!='false' And '$(CopyBuildOutputToPublishDirectory)'=='false'">
      <DllExportIntermediateAssemblyResolvedFileToPublish Include="@(IntermediateAssembly)" Condition="'%(Filename)%(Extension)'!='$(TargetFileName)'" />

      <ResolvedFileToPublish Include="@(DllExportIntermediateAssemblyResolvedFileToPublish)">
        <RelativePath>@(DllExportIntermediateAssemblyResolvedFileToPublish->'%(Filename)%(Extension)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </ResolvedFileToPublish>
    </ItemGroup>
    <!-- .pdb -->
    <ItemGroup Condition="'$(DllExportFallbackDontCopyOutputSymbolsToPublishDirectory)'!='false' And '$(CopyOutputSymbolsToPublishDirectory)'=='false'">
      <DllExportDebugSymbolsIntermediatePathResolvedFileToPublish Include="@(_DebugSymbolsIntermediatePath)" Condition="'%(Filename)'!='$(TargetName)'" />

      <ResolvedFileToPublish Include="@(DllExportDebugSymbolsIntermediatePathResolvedFileToPublish)">
        <RelativePath>@(DllExportDebugSymbolsIntermediatePathResolvedFileToPublish->'%(Filename)%(Extension)')</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile Condition="'$(IncludeSymbolsInSingleFile)'!='true'">true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>

  <Target Condition="'$(DllExportRefreshObj)'=='true'" Name="DllExportRefreshObjTarget" BeforeTargets="ComputeResolvedFilesToPublishList">
    <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(IntermediateOutputPath)"
          OverwriteReadOnlyFiles="true" SkipUnchangedFiles="true" />

    <Copy SourceFiles="$(TargetDir)$(TargetName).pdb" DestinationFolder="$(IntermediateOutputPath)"
          OverwriteReadOnlyFiles="true" SkipUnchangedFiles="true" />
  </Target>


  <!-- Support DllExportImageBase for multiple platforms (TargetFrameworks) /F-324 -->
  <ItemGroup Condition="'$(DisableDllExportImageBaseMultiplePlatforms)'!='true' And '$(TargetFrameworks)'!=''">
    <TfmsXItems Include="$(TargetFrameworks)">
      <Index>$([MSBuild]::Subtract($(TargetFrameworks.Substring(0, $(TargetFrameworks.IndexOf('%(Identity)'))).Split(';').Length), 1))</Index>
    </TfmsXItems>
  </ItemGroup>
  <Target Name="DllExportImageBaseMultiplePlatforms" BeforeTargets="DllExportMod" Condition="'$(DisableDllExportImageBaseMultiplePlatforms)'!='true' And '$(DllExportImageBase)'!='' And '$(DllExportImageBaseStep)'!='' And '$(TargetFrameworks)'!=''">
    <PropertyGroup>
      <DllExportImageBase Condition="$(DllExportImageBase.Contains('x'))==false">$(DllExportImageBase)</DllExportImageBase>
      <DllExportImageBase Condition="$(DllExportImageBase.Contains('x'))==true">$([System.Convert]::ToInt64($(DllExportImageBase), 16))</DllExportImageBase>
      <DllExportImageBaseStep Condition="$(DllExportImageBaseStep.Contains('x'))==false">$(DllExportImageBaseStep)</DllExportImageBaseStep>
      <DllExportImageBaseStep Condition="$(DllExportImageBaseStep.Contains('x'))==true">$([System.Convert]::ToInt64($(DllExportImageBaseStep), 16))</DllExportImageBaseStep>
      <DllExportImageBase Condition="'%(TfmsXItems.Identity)'=='$(TargetFramework)'">$([MSBuild]::Add($(DllExportImageBase), $([MSBuild]::Multiply(%(TfmsXItems.Index), $(DllExportImageBaseStep)))))</DllExportImageBase>
    </PropertyGroup>
  </Target>


  <Target Name="DllExportMod" BeforeTargets="PostBuildEvent" DependsOnTargets="GetFrameworkPaths">
    <PropertyGroup>
      <DllExportModExecuted>false</DllExportModExecuted>
    </PropertyGroup>
    <PropertyGroup>
      <DllExportPlatform Condition="'$(DllExportPlatform)'=='' Or '$(DllExportPlatform)'=='Auto'">$(DllExportDefPlatform)</DllExportPlatform>
      <DllExportCpuType Condition="'$(DllExportCpuType)'==''">$(CpuType)</DllExportCpuType>
      <DllExportLeaveIntermediateFiles Condition="'$(DllExportLeaveIntermediateFiles)'==''">false</DllExportLeaveIntermediateFiles>
      <DllExportTimeout Condition="'$(DllExportTimeout)'==''">45000</DllExportTimeout>
      <DllExportKeyContainer Condition="'$(DllExportKeyContainer)'==''">$(KeyContainerName)$(AssemblyKeyContainerName)</DllExportKeyContainer>
      <DllExportKeyFile Condition="'$(DllExportKeyFile)'==''">$(KeyOriginatorFile)</DllExportKeyFile>
      <DllExportProjectDirectory Condition="'$(DllExportProjectDirectory)'==''">$(MSBuildProjectDirectory)</DllExportProjectDirectory>
      <DllExportInputFileName Condition="'$(DllExportInputFileName)'==''">$(TargetPath)</DllExportInputFileName>
      <DllExportFrameworkPath Condition="'$(DllExportFrameworkPath)'==''">$(DllExportILAsmCustomPath);$(TargetedFrameworkDir);$(TargetFrameworkDirectory)</DllExportFrameworkPath>
      <DllExportLibToolPath Condition="'$(DllExportLibToolPath)'==''">$(DllExportVSRoot)VC\bin</DllExportLibToolPath>
      <DllExportLibToolDllPath Condition="'$(DllExportLibToolDllPath)'==''">$(DllExportVSBin)</DllExportLibToolDllPath>
      <DllExportTargetFrameworkVersion Condition="'$(DllExportTargetFrameworkVersion)'==''">$(TargetFrameworkVersion)</DllExportTargetFrameworkVersion>
      <DllExportTargetFrameworkIdentifier Condition="'$(DllExportTargetFrameworkIdentifier)'==''">$(TargetFrameworkIdentifier)</DllExportTargetFrameworkIdentifier>
      <DllExportSdkPath Condition="'$(DllExportSdkPath)'==''">$(DllExportILAsmCustomPath);$(TargetFrameworkSDKToolsDirectory)</DllExportSdkPath>
      <DllExportSkipOnAnyCpu Condition="'$(DllExportSkipOnAnyCpu)'==''">$(NoDllExportsForAnyCpu)</DllExportSkipOnAnyCpu>
      <DllExportDDNSCecil Condition="'$(DllExportDDNSCecil)'==''">true</DllExportDDNSCecil>
      <DllExportOurILAsm Condition="'$(DllExportOurILAsm)'==''">false</DllExportOurILAsm>
      <DllExportSysObjRebase Condition="'$(DllExportSysObjRebase)'==''">false</DllExportSysObjRebase>
      <DllExportImageBase Condition="'$(DllExportImageBase)'==''">-1</DllExportImageBase>
      <DllExportOrdinalsBase Condition="'$(DllExportOrdinalsBase)'==''">1</DllExportOrdinalsBase>
      <DllExportGenExpLib Condition="'$(DllExportGenExpLib)'==''">false</DllExportGenExpLib>
      <DllExportOurILAsmPath Condition="'$(DllExportOurILAsm)'!='true'"></DllExportOurILAsmPath>
      <DllExportVsDevCmd Condition="'$(DllExportVsDevCmd)'==''">$(DllExportVSRoot)Common7\Tools\VsDevCmd.bat</DllExportVsDevCmd>
      <DllExportVcVarsAll Condition="'$(DllExportVcVarsAll)'==''">$(DllExportVSRoot)VC\vcvarsall.bat;$(DllExportVSRoot)VC\Auxiliary\Build\vcvarsall.bat</DllExportVcVarsAll>
      <DllExportPeCheck Condition="'$(DllExportPeCheck)'==''"></DllExportPeCheck>
      <DllExportPatches Condition="'$(DllExportPatches)'==''"></DllExportPatches>
      <DllExportILAsmExternAsm Condition="'$(DllExportILAsmExternAsm)'==''"></DllExportILAsmExternAsm>
      <DllExportILAsmTypeRef Condition="'$(DllExportILAsmTypeRef)'==''"></DllExportILAsmTypeRef>
      <DllExportRefPackages Condition="'$(DllExportRefPackages)'==''"></DllExportRefPackages>
    </PropertyGroup>

    <PropertyGroup Label="EmitDebugSymbols" Condition="'$(DllExportEmitDebugSymbols)'==''">
      <DllExportEmitDebugSymbols Condition="'$(Optimize)'=='true' And '$(DebugSymbols)'=='false' And '$(DebugType)'=='none'">Optimize</DllExportEmitDebugSymbols>
      <DllExportEmitDebugSymbols Condition="'$(Optimize)'=='true' And '$(DebugSymbols)'=='false' And '$(DebugType)'!='none'">PdbOptimize</DllExportEmitDebugSymbols>
      <DllExportEmitDebugSymbols Condition="'$(Optimize)'=='true' And '$(DebugSymbols)'=='true' And '$(DebugType)'!='none'">DebugOptimize</DllExportEmitDebugSymbols>
      <DllExportEmitDebugSymbols Condition="'$(Optimize)'=='false' And '$(DebugSymbols)'=='true' And '$(DebugType)'!='none'">Debug</DllExportEmitDebugSymbols>
      <DllExportEmitDebugSymbols Condition="'$(Optimize)'=='false' And '$(DebugSymbols)'=='false' And '$(DebugType)'!='none'">DebugImpl</DllExportEmitDebugSymbols>
      <DllExportEmitDebugSymbols Condition="'$(DllExportEmitDebugSymbols)'==''">$(DebugSymbols)</DllExportEmitDebugSymbols> <!-- legacy -->
    </PropertyGroup>

    <DllExportActivatorTask
          Platform="$(DllExportPlatform)"
          CpuType="$(DllExportCpuType)"
          DllExportAttributeFullName="$(DllExportAttributeFullName)"
          EmitDebugSymbols="$(DllExportEmitDebugSymbols)"
          LeaveIntermediateFiles="$(DllExportLeaveIntermediateFiles)"
          Timeout="$(DllExportTimeout)"
          KeyContainer="$(DllExportKeyContainer)"
          KeyFile="$(DllExportKeyFile)"
          ProjectDirectory="$(DllExportProjectDirectory)"
          ProcEnv="$(DllExportProcEnv)"
          InputFileName="$(DllExportInputFileName)"
          FrameworkPath="$(DllExportFrameworkPath)"
          VsDevCmd="$(DllExportVsDevCmd)"
          VcVarsAll="$(DllExportVcVarsAll)"
          LibToolPath="$(DllExportLibToolPath)"
          LibToolDllPath="$(DllExportLibToolDllPath)"
          TargetFrameworkVersion="$(DllExportTargetFrameworkVersion)"
          TargetFrameworkIdentifier="$(DllExportTargetFrameworkIdentifier)"
          SdkPath="$(DllExportSdkPath)"
          SkipOnAnyCpu="$(DllExportSkipOnAnyCpu)"
          ImageBaseRaw="$(DllExportImageBase)"
          OrdinalsBase="$(DllExportOrdinalsBase)"
          GenExpLib="$(DllExportGenExpLib)"
          OurILAsmPath="$(DllExportOurILAsmPath)"
          MetaLib="$(DllExportMetaLibFullPath)"
          PeCheckRaw="$(DllExportPeCheck)"
          PatchesRaw="$(DllExportPatches)"
          SysObjRebase="$(DllExportSysObjRebase)"
          AssemblyExternDirectivesRaw="$(DllExportILAsmExternAsm)"
          TypeRefDirectivesRaw="$(DllExportILAsmTypeRef)"
         />

    <PropertyGroup>
      <DllExportModExecuted>true</DllExportModExecuted>
    </PropertyGroup>
  </Target>
  <UsingTask TaskName="net.r_eg.DllExport.Activator.DllExportActivatorTask" AssemblyFile="net.r_eg.DllExport.Activator.dll" />

  <!-- TargetFrameworks (multi-targeting) support in SDK-style projects: netstandard2.0;net40;net472;net8.0;... /F-284 -->
  <Target Name="DllExportMetaXBaseTarget" BeforeTargets="GenerateTargetFrameworkMonikerAttribute" Inputs="$(TargetFramework)" Outputs="$(DllExportMetaXBase)">
    <PropertyGroup><!-- MSBuild 16+ -->
      <IsNetCoreBased Condition="'$(TargetFrameworkIdentifier)'=='.NETCoreApp'">true</IsNetCoreBased>
    </PropertyGroup>
    <PropertyGroup Condition="'$(TargetFrameworkIdentifier)'==''"> <!-- Fallback -->
      <_tfmlower>$(TargetFramework.ToLower())</_tfmlower>
      <IsNetCoreBased Condition="$(_tfmlower.StartsWith('netc')) Or $(_tfmlower.StartsWith('nets')) Or ('$(_tfmlower)'!='' And $([System.Char]::IsDigit($(_tfmlower[3]))) And '$(_tfmlower[3])' &gt;= '5')">true</IsNetCoreBased>
    </PropertyGroup>
    <PropertyGroup>
      <DllExportMetaXBase Condition="'$(IsNetCoreBased)'=='true'">$(DllExportDirMetaCore)</DllExportMetaXBase>
      <DllExportMetaXBase Condition="'$(IsNetCoreBased)'!='true'">$(DllExportDirMetaFX)</DllExportMetaXBase>
      <DllExportLibPath Condition="'$(DllExportLibPath)'==''">gcache\$(DllExportMetaXBase)\$(DllExportNamespace)\</DllExportLibPath>
    </PropertyGroup>
  </Target>

</Project>