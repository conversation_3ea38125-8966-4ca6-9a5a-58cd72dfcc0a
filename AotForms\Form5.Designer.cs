namespace Client
{
    partial class Form5
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;


        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form5));
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges61 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges62 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges49 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges50 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges33 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges34 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges23 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges24 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges25 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges26 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges27 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges28 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges31 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges32 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges29 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges30 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges47 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges48 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges35 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges36 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges37 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges38 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges39 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges40 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges41 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges42 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges97 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges98 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges81 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges82 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges63 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges64 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges65 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges66 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges67 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges68 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges69 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges70 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges71 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges72 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges73 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges74 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges75 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges76 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges79 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges80 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges77 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges78 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges95 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges96 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges83 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges84 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges85 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges86 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges87 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges88 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges89 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges90 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges93 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges94 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges91 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges92 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges45 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges46 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges43 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges44 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges51 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges52 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges53 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges54 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges55 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges56 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges59 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges60 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges57 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges58 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges137 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges138 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges121 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges122 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges99 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges100 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges101 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges102 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges103 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges104 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges105 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges106 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges107 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges108 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges109 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges110 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges111 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges112 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges113 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges114 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges115 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges116 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges119 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges120 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges117 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges118 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges135 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges136 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges123 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges124 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges125 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges126 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges127 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges128 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges129 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges130 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges133 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges134 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges131 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges132 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges139 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges140 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges141 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges142 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges145 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges146 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges143 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges144 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges147 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges148 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges151 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges152 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges149 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges150 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges153 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges154 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges155 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges156 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges157 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges158 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges159 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges160 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges161 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges162 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges163 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges164 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel3 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
            label2 = new Label();
            guna2Button15 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            guna2Panel10 = new Guna.UI2.WinForms.Guna2Panel();
            label28 = new Label();
            guna2Panel7 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel5 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox20 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label62 = new Label();
            guna2CustomCheckBox1 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox4 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox3 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox2 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button10 = new Guna.UI2.WinForms.Guna2Button();
            label15 = new Label();
            label16 = new Label();
            label12 = new Label();
            label14 = new Label();
            label10 = new Label();
            label11 = new Label();
            label13 = new Label();
            label9 = new Label();
            guna2Panel9 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox3 = new Guna.UI2.WinForms.Guna2PictureBox();
            label4 = new Label();
            guna2Panel6 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox5 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox6 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox7 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox8 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label17 = new Label();
            label18 = new Label();
            label19 = new Label();
            label21 = new Label();
            guna2Panel16 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel17 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox14 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox15 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox16 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox17 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Button6 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button7 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button8 = new Guna.UI2.WinForms.Guna2Button();
            label51 = new Label();
            label52 = new Label();
            label53 = new Label();
            label54 = new Label();
            label55 = new Label();
            label56 = new Label();
            label57 = new Label();
            label58 = new Label();
            guna2Panel18 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox7 = new Guna.UI2.WinForms.Guna2PictureBox();
            label7 = new Label();
            guna2Panel19 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox19 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox18 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel26 = new Guna.UI2.WinForms.Guna2Panel();
            label49 = new Label();
            label48 = new Label();
            guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
            label46 = new Label();
            label47 = new Label();
            label44 = new Label();
            label45 = new Label();
            label42 = new Label();
            label43 = new Label();
            label35 = new Label();
            label20 = new Label();
            pictureBox6 = new PictureBox();
            guna2Panel20 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox6 = new Guna.UI2.WinForms.Guna2PictureBox();
            label8 = new Label();
            label22 = new Label();
            label23 = new Label();
            label24 = new Label();
            label25 = new Label();
            guna2Panel8 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox2 = new Guna.UI2.WinForms.Guna2PictureBox();
            label3 = new Label();
            guna2ProgressBar3 = new Guna.UI2.WinForms.Guna2ProgressBar();
            guna2ProgressBar2 = new Guna.UI2.WinForms.Guna2ProgressBar();
            guna2ProgressBar1 = new Guna.UI2.WinForms.Guna2ProgressBar();
            guna2Panel4 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox1 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2Panel12 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel11 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox11 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2PictureBox10 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2PictureBox9 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2PictureBox8 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2CustomCheckBox9 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox10 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox11 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CustomCheckBox12 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2ComboBox1 = new Guna.UI2.WinForms.Guna2ComboBox();
            label29 = new Label();
            label30 = new Label();
            label31 = new Label();
            label32 = new Label();
            label33 = new Label();
            label34 = new Label();
            label36 = new Label();
            label37 = new Label();
            guna2Panel13 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox4 = new Guna.UI2.WinForms.Guna2PictureBox();
            label5 = new Label();
            guna2Panel14 = new Guna.UI2.WinForms.Guna2Panel();
            label61 = new Label();
            guna2Panel22 = new Guna.UI2.WinForms.Guna2Panel();
            label60 = new Label();
            guna2Panel30 = new Guna.UI2.WinForms.Guna2Panel();
            label59 = new Label();
            guna2CustomCheckBox13 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label50 = new Label();
            guna2Panel24 = new Guna.UI2.WinForms.Guna2Panel();
            label41 = new Label();
            label38 = new Label();
            label39 = new Label();
            guna2Panel15 = new Guna.UI2.WinForms.Guna2Panel();
            guna2PictureBox5 = new Guna.UI2.WinForms.Guna2PictureBox();
            label6 = new Label();
            label40 = new Label();
            label26 = new Label();
            guna2ProgressBar4 = new Guna.UI2.WinForms.Guna2ProgressBar();
            label27 = new Label();
            timer1 = new System.Windows.Forms.Timer(components);
            guna2Panel23 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel21 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel25 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Button9 = new Guna.UI2.WinForms.Guna2Button();
            guna2Panel111 = new Guna.UI2.WinForms.Guna2Panel();
            label1 = new Label();
            guna2Button11 = new Guna.UI2.WinForms.Guna2Button();
            guna2Panel29 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel28 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel27 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Button12 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button18 = new Guna.UI2.WinForms.Guna2Button();
            timer2 = new System.Windows.Forms.Timer(components);
            timer3 = new System.Windows.Forms.Timer(components);
            timer4 = new System.Windows.Forms.Timer(components);
            weaponIconPictureBox = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2Panel2.SuspendLayout();
            guna2Panel10.SuspendLayout();
            guna2Panel7.SuspendLayout();
            guna2Panel5.SuspendLayout();
            guna2Panel9.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).BeginInit();
            guna2Panel6.SuspendLayout();
            guna2Panel16.SuspendLayout();
            guna2Panel17.SuspendLayout();
            guna2Panel18.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).BeginInit();
            guna2Panel19.SuspendLayout();
            guna2Panel26.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox6).BeginInit();
            guna2Panel20.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox6).BeginInit();
            guna2Panel8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).BeginInit();
            guna2Panel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox1).BeginInit();
            guna2Panel12.SuspendLayout();
            guna2Panel11.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox11).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox10).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox9).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox8).BeginInit();
            guna2Panel13.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox4).BeginInit();
            guna2Panel14.SuspendLayout();
            guna2Panel15.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox5).BeginInit();
            guna2Panel23.SuspendLayout();
            guna2Panel21.SuspendLayout();
            guna2Panel25.SuspendLayout();
            guna2Panel111.SuspendLayout();
            guna2Panel27.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)weaponIconPictureBox).BeginInit();
            SuspendLayout();
            //
            // guna2Panel2
            //
            guna2Panel2.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel2.Controls.Add(guna2Panel3);
            guna2Panel2.Controls.Add(guna2Panel1);
            guna2Panel2.CustomizableEdges = customizableEdges5;
            guna2Panel2.FillColor = Color.FromArgb(12, 12, 12);
            guna2Panel2.ForeColor = Color.Black;
            guna2Panel2.Location = new Point(4, 5);
            guna2Panel2.Margin = new Padding(4, 3, 4, 3);
            guna2Panel2.Name = "guna2Panel2";
            guna2Panel2.ShadowDecoration.Color = Color.White;
            guna2Panel2.ShadowDecoration.CustomizableEdges = customizableEdges6;
            guna2Panel2.ShadowDecoration.Depth = 50;
            guna2Panel2.ShadowDecoration.Enabled = true;
            guna2Panel2.Size = new Size(244, 182);
            guna2Panel2.TabIndex = 421;
            //
            // guna2Panel3
            //
            guna2Panel3.CustomizableEdges = customizableEdges1;
            guna2Panel3.Location = new Point(8, 164);
            guna2Panel3.Margin = new Padding(4, 3, 4, 3);
            guna2Panel3.Name = "guna2Panel3";
            guna2Panel3.ShadowDecoration.Color = Color.White;
            guna2Panel3.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2Panel3.ShadowDecoration.Depth = 50;
            guna2Panel3.ShadowDecoration.Enabled = true;
            guna2Panel3.Size = new Size(227, 6);
            guna2Panel3.TabIndex = 423;
            guna2Panel3.Paint += guna2Panel3_Paint_1;
            //
            // guna2Panel1
            //
            guna2Panel1.CustomizableEdges = customizableEdges3;
            guna2Panel1.ForeColor = Color.FromArgb(12, 12, 12);
            guna2Panel1.Location = new Point(8, 10);
            guna2Panel1.Margin = new Padding(4, 3, 4, 3);
            guna2Panel1.Name = "guna2Panel1";
            guna2Panel1.ShadowDecoration.Color = Color.White;
            guna2Panel1.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2Panel1.ShadowDecoration.Depth = 50;
            guna2Panel1.ShadowDecoration.Enabled = true;
            guna2Panel1.Size = new Size(227, 140);
            guna2Panel1.TabIndex = 422;
            guna2Panel1.Paint += guna2Panel1_Paint_1;
            //
            // label2
            //
            label2.AutoSize = true;
            label2.Location = new Point(976, 374);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(38, 15);
            label2.TabIndex = 422;
            label2.Text = "label2";
            //
            // guna2Button15
            //
            guna2Button15.Animated = true;
            guna2Button15.BackColor = Color.FromArgb(20, 20, 22);
            guna2Button15.BorderColor = Color.FromArgb(20, 20, 22);
            guna2Button15.BorderRadius = 3;
            guna2Button15.BorderThickness = 1;
            guna2Button15.ButtonMode = Guna.UI2.WinForms.Enums.ButtonMode.ToogleButton;
            guna2Button15.CustomizableEdges = customizableEdges7;
            guna2Button15.DisabledState.BorderColor = Color.DarkGray;
            guna2Button15.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button15.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button15.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button15.FillColor = Color.Transparent;
            guna2Button15.FocusedColor = Color.Transparent;
            guna2Button15.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button15.ForeColor = Color.White;
            guna2Button15.HoverState.FillColor = Color.Transparent;
            guna2Button15.Image = (Image)resources.GetObject("guna2Button15.Image");
            guna2Button15.ImageSize = new Size(21, 21);
            guna2Button15.Location = new Point(7, 104);
            guna2Button15.Margin = new Padding(4, 3, 4, 3);
            guna2Button15.Name = "guna2Button15";
            guna2Button15.PressedColor = Color.Transparent;
            guna2Button15.ShadowDecoration.Color = Color.Gray;
            guna2Button15.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2Button15.Size = new Size(47, 47);
            guna2Button15.TabIndex = 433;
            guna2Button15.Click += guna2Button15_Click;
            //
            // guna2Button2
            //
            guna2Button2.Animated = true;
            guna2Button2.BackColor = Color.FromArgb(20, 20, 22);
            guna2Button2.BorderColor = Color.FromArgb(20, 20, 22);
            guna2Button2.BorderRadius = 3;
            guna2Button2.BorderThickness = 1;
            guna2Button2.ButtonMode = Guna.UI2.WinForms.Enums.ButtonMode.ToogleButton;
            guna2Button2.CustomizableEdges = customizableEdges9;
            guna2Button2.DisabledState.BorderColor = Color.DarkGray;
            guna2Button2.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button2.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button2.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button2.FillColor = Color.Transparent;
            guna2Button2.FocusedColor = Color.Transparent;
            guna2Button2.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button2.ForeColor = Color.White;
            guna2Button2.HoverState.FillColor = Color.Transparent;
            guna2Button2.Image = (Image)resources.GetObject("guna2Button2.Image");
            guna2Button2.ImageSize = new Size(21, 21);
            guna2Button2.Location = new Point(8, 186);
            guna2Button2.Margin = new Padding(4, 3, 4, 3);
            guna2Button2.Name = "guna2Button2";
            guna2Button2.PressedColor = Color.Transparent;
            guna2Button2.ShadowDecoration.Color = Color.Gray;
            guna2Button2.ShadowDecoration.CustomizableEdges = customizableEdges10;
            guna2Button2.Size = new Size(47, 47);
            guna2Button2.TabIndex = 435;
            guna2Button2.Click += guna2Button2_Click;
            //
            // guna2Button1
            //
            guna2Button1.Animated = true;
            guna2Button1.BackColor = Color.FromArgb(20, 20, 22);
            guna2Button1.BorderColor = Color.FromArgb(20, 20, 22);
            guna2Button1.BorderRadius = 3;
            guna2Button1.BorderThickness = 1;
            guna2Button1.ButtonMode = Guna.UI2.WinForms.Enums.ButtonMode.ToogleButton;
            guna2Button1.CustomizableEdges = customizableEdges11;
            guna2Button1.DisabledState.BorderColor = Color.DarkGray;
            guna2Button1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button1.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button1.FillColor = Color.Transparent;
            guna2Button1.FocusedColor = Color.Transparent;
            guna2Button1.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button1.ForeColor = Color.White;
            guna2Button1.HoverState.FillColor = Color.Transparent;
            guna2Button1.Image = (Image)resources.GetObject("guna2Button1.Image");
            guna2Button1.ImageSize = new Size(21, 21);
            guna2Button1.Location = new Point(9, 269);
            guna2Button1.Margin = new Padding(4, 3, 4, 3);
            guna2Button1.Name = "guna2Button1";
            guna2Button1.PressedColor = Color.Transparent;
            guna2Button1.ShadowDecoration.Color = Color.Gray;
            guna2Button1.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2Button1.Size = new Size(47, 47);
            guna2Button1.TabIndex = 436;
            guna2Button1.Click += guna2Button1_Click;
            //
            // guna2Panel10
            //
            guna2Panel10.BackColor = Color.FromArgb(12, 12, 12);
            guna2Panel10.BorderRadius = 10;
            guna2Panel10.BorderThickness = 1;
            guna2Panel10.Controls.Add(label28);
            guna2Panel10.Controls.Add(guna2Panel7);
            guna2Panel10.Controls.Add(guna2ProgressBar3);
            guna2Panel10.Controls.Add(guna2ProgressBar2);
            guna2Panel10.Controls.Add(guna2ProgressBar1);
            guna2Panel10.Controls.Add(guna2Panel4);
            guna2Panel10.CustomBorderColor = Color.FromArgb(12, 12, 12);
            guna2Panel10.CustomBorderThickness = new Padding(7);
            guna2Panel10.CustomizableEdges = customizableEdges61;
            guna2Panel10.ForeColor = Color.White;
            guna2Panel10.Location = new Point(-5, 1);
            guna2Panel10.Margin = new Padding(4, 3, 4, 3);
            guna2Panel10.Name = "guna2Panel10";
            guna2Panel10.ShadowDecoration.Color = Color.FromArgb(224, 224, 224);
            guna2Panel10.ShadowDecoration.CustomizableEdges = customizableEdges62;
            guna2Panel10.ShadowDecoration.Depth = 50;
            guna2Panel10.Size = new Size(849, 532);
            guna2Panel10.TabIndex = 437;
            guna2Panel10.Paint += guna2Panel10_Paint;
            //
            // label28
            //
            label28.AutoSize = true;
            label28.BackColor = Color.Transparent;
            label28.Font = new Font("Microsoft Sans Serif", 20.25F, FontStyle.Bold, GraphicsUnit.Point);
            label28.ForeColor = Color.White;
            label28.Location = new Point(78, 2);
            label28.Margin = new Padding(4, 0, 4, 0);
            label28.Name = "label28";
            label28.Size = new Size(112, 31);
            label28.TabIndex = 441;
            label28.Text = "Niguzz ";
            //
            // guna2Panel7
            //
            guna2Panel7.BackColor = Color.FromArgb(12, 12, 12);
            guna2Panel7.Controls.Add(guna2Panel5);
            guna2Panel7.Controls.Add(guna2Panel6);
            guna2Panel7.CustomizableEdges = customizableEdges49;
            guna2Panel7.Location = new Point(74, 39);
            guna2Panel7.Margin = new Padding(4, 3, 4, 3);
            guna2Panel7.Name = "guna2Panel7";
            guna2Panel7.ShadowDecoration.CustomizableEdges = customizableEdges50;
            guna2Panel7.Size = new Size(756, 486);
            guna2Panel7.TabIndex = 440;
            guna2Panel7.Paint += guna2Panel7_Paint;
            //
            // guna2Panel5
            //
            guna2Panel5.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel5.Controls.Add(guna2CustomCheckBox20);
            guna2Panel5.Controls.Add(label62);
            guna2Panel5.Controls.Add(guna2CustomCheckBox1);
            guna2Panel5.Controls.Add(guna2CustomCheckBox4);
            guna2Panel5.Controls.Add(guna2CustomCheckBox3);
            guna2Panel5.Controls.Add(guna2CustomCheckBox2);
            guna2Panel5.Controls.Add(guna2Button4);
            guna2Panel5.Controls.Add(guna2Button3);
            guna2Panel5.Controls.Add(guna2Button10);
            guna2Panel5.Controls.Add(label15);
            guna2Panel5.Controls.Add(label16);
            guna2Panel5.Controls.Add(label12);
            guna2Panel5.Controls.Add(label14);
            guna2Panel5.Controls.Add(label10);
            guna2Panel5.Controls.Add(label11);
            guna2Panel5.Controls.Add(label13);
            guna2Panel5.Controls.Add(label9);
            guna2Panel5.Controls.Add(guna2Panel9);
            guna2Panel5.CustomizableEdges = customizableEdges33;
            guna2Panel5.Location = new Point(24, 23);
            guna2Panel5.Margin = new Padding(4, 3, 4, 3);
            guna2Panel5.Name = "guna2Panel5";
            guna2Panel5.ShadowDecoration.CustomizableEdges = customizableEdges34;
            guna2Panel5.Size = new Size(350, 452);
            guna2Panel5.TabIndex = 438;
            guna2Panel5.Paint += guna2Panel5_Paint;
            //
            // guna2CustomCheckBox20
            //
            guna2CustomCheckBox20.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox20.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox20.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox20.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox20.CustomizableEdges = customizableEdges13;
            guna2CustomCheckBox20.Location = new Point(289, 311);
            guna2CustomCheckBox20.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox20.Name = "guna2CustomCheckBox20";
            guna2CustomCheckBox20.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox20.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2CustomCheckBox20.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox20.Size = new Size(23, 23);
            guna2CustomCheckBox20.TabIndex = 444;
            guna2CustomCheckBox20.Text = "guna2CustomCheckBox20";
            guna2CustomCheckBox20.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox20.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox20.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox20.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox20.Click += guna2CustomCheckBox20_Click_1;
            //
            // label62
            //
            label62.AutoSize = true;
            label62.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label62.ForeColor = Color.Gray;
            label62.Location = new Point(30, 313);
            label62.Margin = new Padding(2, 0, 2, 0);
            label62.Name = "label62";
            label62.Size = new Size(71, 18);
            label62.TabIndex = 443;
            label62.Text = "No Ricoil";
            //
            // guna2CustomCheckBox1
            //
            guna2CustomCheckBox1.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox1.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox1.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox1.CustomizableEdges = customizableEdges15;
            guna2CustomCheckBox1.Location = new Point(289, 84);
            guna2CustomCheckBox1.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox1.Name = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox1.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2CustomCheckBox1.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox1.Size = new Size(23, 23);
            guna2CustomCheckBox1.TabIndex = 439;
            guna2CustomCheckBox1.Text = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox1.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox1.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox1.Click += guna2CustomCheckBox1_Click;
            //
            // guna2CustomCheckBox4
            //
            guna2CustomCheckBox4.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox4.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox4.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox4.CustomizableEdges = customizableEdges17;
            guna2CustomCheckBox4.Location = new Point(289, 143);
            guna2CustomCheckBox4.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox4.Name = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox4.ShadowDecoration.CustomizableEdges = customizableEdges18;
            guna2CustomCheckBox4.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox4.Size = new Size(23, 23);
            guna2CustomCheckBox4.TabIndex = 442;
            guna2CustomCheckBox4.Text = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox4.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox4.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2CustomCheckBox3
            //
            guna2CustomCheckBox3.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox3.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox3.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox3.CustomizableEdges = customizableEdges19;
            guna2CustomCheckBox3.Location = new Point(289, 201);
            guna2CustomCheckBox3.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox3.Name = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox3.ShadowDecoration.CustomizableEdges = customizableEdges20;
            guna2CustomCheckBox3.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox3.Size = new Size(23, 23);
            guna2CustomCheckBox3.TabIndex = 441;
            guna2CustomCheckBox3.Text = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox3.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox3.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2CustomCheckBox2
            //
            guna2CustomCheckBox2.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox2.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox2.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox2.CustomizableEdges = customizableEdges21;
            guna2CustomCheckBox2.Location = new Point(289, 258);
            guna2CustomCheckBox2.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox2.Name = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox2.ShadowDecoration.CustomizableEdges = customizableEdges22;
            guna2CustomCheckBox2.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox2.Size = new Size(23, 23);
            guna2CustomCheckBox2.TabIndex = 440;
            guna2CustomCheckBox2.Text = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox2.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox2.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox2.Click += guna2CustomCheckBox2_Click;
            //
            // guna2Button4
            //
            guna2Button4.CustomizableEdges = customizableEdges23;
            guna2Button4.DisabledState.BorderColor = Color.DarkGray;
            guna2Button4.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button4.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button4.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button4.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button4.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button4.ForeColor = Color.DimGray;
            guna2Button4.Image = (Image)resources.GetObject("guna2Button4.Image");
            guna2Button4.Location = new Point(156, 203);
            guna2Button4.Margin = new Padding(4, 3, 4, 3);
            guna2Button4.Name = "guna2Button4";
            guna2Button4.ShadowDecoration.Color = Color.DimGray;
            guna2Button4.ShadowDecoration.CustomizableEdges = customizableEdges24;
            guna2Button4.ShadowDecoration.Enabled = true;
            guna2Button4.Size = new Size(93, 21);
            guna2Button4.TabIndex = 429;
            guna2Button4.Text = "None";
            guna2Button4.Click += guna2Button4_Click;
            //
            // guna2Button3
            //
            guna2Button3.CustomizableEdges = customizableEdges25;
            guna2Button3.DisabledState.BorderColor = Color.DarkGray;
            guna2Button3.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button3.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button3.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button3.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button3.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button3.ForeColor = Color.DimGray;
            guna2Button3.Image = (Image)resources.GetObject("guna2Button3.Image");
            guna2Button3.Location = new Point(156, 143);
            guna2Button3.Margin = new Padding(4, 3, 4, 3);
            guna2Button3.Name = "guna2Button3";
            guna2Button3.ShadowDecoration.Color = Color.DimGray;
            guna2Button3.ShadowDecoration.CustomizableEdges = customizableEdges26;
            guna2Button3.ShadowDecoration.Enabled = true;
            guna2Button3.Size = new Size(93, 21);
            guna2Button3.TabIndex = 428;
            guna2Button3.Text = "None";
            //
            // guna2Button10
            //
            guna2Button10.CustomizableEdges = customizableEdges27;
            guna2Button10.DisabledState.BorderColor = Color.DarkGray;
            guna2Button10.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button10.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button10.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button10.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button10.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button10.ForeColor = Color.DimGray;
            guna2Button10.Image = (Image)resources.GetObject("guna2Button10.Image");
            guna2Button10.Location = new Point(156, 84);
            guna2Button10.Margin = new Padding(4, 3, 4, 3);
            guna2Button10.Name = "guna2Button10";
            guna2Button10.ShadowDecoration.Color = Color.DimGray;
            guna2Button10.ShadowDecoration.CustomizableEdges = customizableEdges28;
            guna2Button10.ShadowDecoration.Enabled = true;
            guna2Button10.Size = new Size(93, 21);
            guna2Button10.TabIndex = 427;
            guna2Button10.Text = "None";
            //
            // label15
            //
            label15.AutoSize = true;
            label15.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label15.ForeColor = Color.Gray;
            label15.Location = new Point(31, 282);
            label15.Margin = new Padding(2, 0, 2, 0);
            label15.Name = "label15";
            label15.Size = new Size(57, 13);
            label15.TabIndex = 425;
            label15.Text = "In Training";
            //
            // label16
            //
            label16.AutoSize = true;
            label16.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label16.ForeColor = Color.Gray;
            label16.Location = new Point(30, 258);
            label16.Margin = new Padding(2, 0, 2, 0);
            label16.Name = "label16";
            label16.Size = new Size(71, 18);
            label16.TabIndex = 426;
            label16.Text = "No Ricoil";
            //
            // label12
            //
            label12.AutoSize = true;
            label12.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label12.ForeColor = Color.Gray;
            label12.Location = new Point(31, 218);
            label12.Margin = new Padding(2, 0, 2, 0);
            label12.Name = "label12";
            label12.Size = new Size(47, 13);
            label12.TabIndex = 422;
            label12.Text = "In Game";
            //
            // label14
            //
            label14.AutoSize = true;
            label14.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label14.ForeColor = Color.Gray;
            label14.Location = new Point(30, 195);
            label14.Margin = new Padding(2, 0, 2, 0);
            label14.Name = "label14";
            label14.Size = new Size(71, 18);
            label14.TabIndex = 423;
            label14.Text = "AimAsist";
            //
            // label10
            //
            label10.AutoSize = true;
            label10.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label10.ForeColor = Color.Gray;
            label10.Location = new Point(31, 158);
            label10.Margin = new Padding(2, 0, 2, 0);
            label10.Name = "label10";
            label10.Size = new Size(47, 13);
            label10.TabIndex = 419;
            label10.Text = "In Game";
            //
            // label11
            //
            label11.AutoSize = true;
            label11.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label11.ForeColor = Color.Gray;
            label11.Location = new Point(30, 135);
            label11.Margin = new Padding(2, 0, 2, 0);
            label11.Name = "label11";
            label11.Size = new Size(72, 18);
            label11.TabIndex = 420;
            label11.Text = "AimNeck";
            //
            // label13
            //
            label13.AutoSize = true;
            label13.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label13.ForeColor = Color.Gray;
            label13.Location = new Point(33, 97);
            label13.Margin = new Padding(2, 0, 2, 0);
            label13.Name = "label13";
            label13.Size = new Size(47, 13);
            label13.TabIndex = 416;
            label13.Text = "In Game";
            //
            // label9
            //
            label9.AutoSize = true;
            label9.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label9.ForeColor = Color.Gray;
            label9.Location = new Point(31, 74);
            label9.Margin = new Padding(2, 0, 2, 0);
            label9.Name = "label9";
            label9.Size = new Size(58, 18);
            label9.TabIndex = 417;
            label9.Text = "Aimbot";
            //
            // guna2Panel9
            //
            guna2Panel9.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel9.Controls.Add(guna2PictureBox3);
            guna2Panel9.Controls.Add(label4);
            guna2Panel9.CustomizableEdges = customizableEdges31;
            guna2Panel9.Location = new Point(2, 3);
            guna2Panel9.Margin = new Padding(4, 3, 4, 3);
            guna2Panel9.Name = "guna2Panel9";
            guna2Panel9.ShadowDecoration.CustomizableEdges = customizableEdges32;
            guna2Panel9.Size = new Size(346, 35);
            guna2Panel9.TabIndex = 1;
            //
            // guna2PictureBox3
            //
            guna2PictureBox3.CustomizableEdges = customizableEdges29;
            guna2PictureBox3.Image = (Image)resources.GetObject("guna2PictureBox3.Image");
            guna2PictureBox3.ImageRotate = 0F;
            guna2PictureBox3.InitialImage = (Image)resources.GetObject("guna2PictureBox3.InitialImage");
            guna2PictureBox3.Location = new Point(57, 5);
            guna2PictureBox3.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox3.Name = "guna2PictureBox3";
            guna2PictureBox3.ShadowDecoration.CustomizableEdges = customizableEdges30;
            guna2PictureBox3.Size = new Size(55, 27);
            guna2PictureBox3.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox3.TabIndex = 447;
            guna2PictureBox3.TabStop = false;
            //
            // label4
            //
            label4.AutoSize = true;
            label4.BackColor = Color.Transparent;
            label4.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label4.ForeColor = Color.White;
            label4.Location = new Point(121, 3);
            label4.Margin = new Padding(4, 0, 4, 0);
            label4.Name = "label4";
            label4.Size = new Size(96, 24);
            label4.TabIndex = 424;
            label4.Text = "Aim Asist";
            //
            // guna2Panel6
            //
            guna2Panel6.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel6.Controls.Add(guna2CustomCheckBox5);
            guna2Panel6.Controls.Add(guna2CustomCheckBox6);
            guna2Panel6.Controls.Add(guna2CustomCheckBox7);
            guna2Panel6.Controls.Add(guna2CustomCheckBox8);
            guna2Panel6.Controls.Add(label17);
            guna2Panel6.Controls.Add(label18);
            guna2Panel6.Controls.Add(label19);
            guna2Panel6.Controls.Add(label21);
            guna2Panel6.Controls.Add(label22);
            guna2Panel6.Controls.Add(label23);
            guna2Panel6.Controls.Add(label24);
            guna2Panel6.Controls.Add(label25);
            guna2Panel6.Controls.Add(guna2Panel8);
            guna2Panel6.CustomizableEdges = customizableEdges47;
            guna2Panel6.ForeColor = Color.White;
            guna2Panel6.Location = new Point(390, 23);
            guna2Panel6.Margin = new Padding(4, 3, 4, 3);
            guna2Panel6.Name = "guna2Panel6";
            guna2Panel6.ShadowDecoration.Color = Color.WhiteSmoke;
            guna2Panel6.ShadowDecoration.CustomizableEdges = customizableEdges48;
            guna2Panel6.Size = new Size(350, 452);
            guna2Panel6.TabIndex = 439;
            guna2Panel6.Paint += guna2Panel6_Paint;
            //
            // guna2CustomCheckBox5
            //
            guna2CustomCheckBox5.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox5.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox5.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox5.CustomizableEdges = customizableEdges35;
            guna2CustomCheckBox5.Location = new Point(286, 87);
            guna2CustomCheckBox5.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox5.Name = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox5.ShadowDecoration.CustomizableEdges = customizableEdges36;
            guna2CustomCheckBox5.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox5.Size = new Size(23, 23);
            guna2CustomCheckBox5.TabIndex = 443;
            guna2CustomCheckBox5.Text = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox5.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox5.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox5.Click += guna2CustomCheckBox5_Click;
            //
            // guna2CustomCheckBox6
            //
            guna2CustomCheckBox6.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox6.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox6.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox6.CustomizableEdges = customizableEdges37;
            guna2CustomCheckBox6.Location = new Point(286, 145);
            guna2CustomCheckBox6.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox6.Name = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox6.ShadowDecoration.CustomizableEdges = customizableEdges38;
            guna2CustomCheckBox6.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox6.Size = new Size(23, 23);
            guna2CustomCheckBox6.TabIndex = 446;
            guna2CustomCheckBox6.Text = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox6.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox6.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2CustomCheckBox7
            //
            guna2CustomCheckBox7.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox7.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox7.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox7.CustomizableEdges = customizableEdges39;
            guna2CustomCheckBox7.Location = new Point(286, 203);
            guna2CustomCheckBox7.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox7.Name = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox7.ShadowDecoration.CustomizableEdges = customizableEdges40;
            guna2CustomCheckBox7.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox7.Size = new Size(23, 23);
            guna2CustomCheckBox7.TabIndex = 445;
            guna2CustomCheckBox7.Text = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox7.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox7.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox7.Click += guna2CustomCheckBox7_Click;
            //
            // guna2CustomCheckBox8
            //
            guna2CustomCheckBox8.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox8.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox8.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox8.CustomizableEdges = customizableEdges41;
            guna2CustomCheckBox8.Location = new Point(286, 261);
            guna2CustomCheckBox8.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox8.Name = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox8.ShadowDecoration.CustomizableEdges = customizableEdges42;
            guna2CustomCheckBox8.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox8.Size = new Size(23, 23);
            guna2CustomCheckBox8.TabIndex = 444;
            guna2CustomCheckBox8.Text = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox8.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox8.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox8.Click += guna2CustomCheckBox8_Click;
            //
            // label17
            //
            label17.AutoSize = true;
            label17.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label17.ForeColor = Color.Gray;
            label17.Location = new Point(26, 284);
            label17.Margin = new Padding(2, 0, 2, 0);
            label17.Name = "label17";
            label17.Size = new Size(36, 13);
            label17.TabIndex = 437;
            label17.Text = "Lobby";
            //
            // label18
            //
            label18.AutoSize = true;
            label18.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label18.ForeColor = Color.Gray;
            label18.Location = new Point(24, 261);
            label18.Margin = new Padding(2, 0, 2, 0);
            label18.Name = "label18";
            label18.Size = new Size(54, 18);
            label18.TabIndex = 438;
            label18.Text = "Fix - D";
            //
            // label19
            //
            label19.AutoSize = true;
            label19.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Gray;
            label19.Location = new Point(26, 220);
            label19.Margin = new Padding(2, 0, 2, 0);
            label19.Name = "label19";
            label19.Size = new Size(36, 13);
            label19.TabIndex = 434;
            label19.Text = "Lobby";
            //
            // label21
            //
            label21.AutoSize = true;
            label21.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label21.ForeColor = Color.Gray;
            label21.Location = new Point(24, 197);
            label21.Margin = new Padding(2, 0, 2, 0);
            label21.Name = "label21";
            label21.Size = new Size(102, 18);
            label21.TabIndex = 435;
            label21.Text = "Awm - Switch";
            //
            // guna2Panel16
            //
            guna2Panel16.BackColor = Color.FromArgb(12, 12, 12);
            guna2Panel16.Controls.Add(guna2Panel17);
            guna2Panel16.Controls.Add(guna2Panel19);
            guna2Panel16.CustomizableEdges = customizableEdges97;
            guna2Panel16.Location = new Point(669, 539);
            guna2Panel16.Margin = new Padding(4, 3, 4, 3);
            guna2Panel16.Name = "guna2Panel16";
            guna2Panel16.ShadowDecoration.CustomizableEdges = customizableEdges98;
            guna2Panel16.Size = new Size(722, 354);
            guna2Panel16.TabIndex = 442;
            guna2Panel16.Paint += guna2Panel16_Paint;
            //
            // guna2Panel17
            //
            guna2Panel17.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel17.Controls.Add(guna2CustomCheckBox14);
            guna2Panel17.Controls.Add(guna2CustomCheckBox15);
            guna2Panel17.Controls.Add(guna2CustomCheckBox16);
            guna2Panel17.Controls.Add(guna2CustomCheckBox17);
            guna2Panel17.Controls.Add(guna2Button6);
            guna2Panel17.Controls.Add(guna2Button7);
            guna2Panel17.Controls.Add(guna2Button8);
            guna2Panel17.Controls.Add(label51);
            guna2Panel17.Controls.Add(label52);
            guna2Panel17.Controls.Add(label53);
            guna2Panel17.Controls.Add(label54);
            guna2Panel17.Controls.Add(label55);
            guna2Panel17.Controls.Add(label56);
            guna2Panel17.Controls.Add(label57);
            guna2Panel17.Controls.Add(label58);
            guna2Panel17.Controls.Add(guna2Panel18);
            guna2Panel17.CustomizableEdges = customizableEdges81;
            guna2Panel17.ForeColor = Color.White;
            guna2Panel17.Location = new Point(24, 23);
            guna2Panel17.Margin = new Padding(4, 3, 4, 3);
            guna2Panel17.Name = "guna2Panel17";
            guna2Panel17.ShadowDecoration.CustomizableEdges = customizableEdges82;
            guna2Panel17.Size = new Size(350, 452);
            guna2Panel17.TabIndex = 438;
            guna2Panel17.Paint += guna2Panel17_Paint;
            //
            // guna2CustomCheckBox14
            //
            guna2CustomCheckBox14.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox14.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox14.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox14.CustomizableEdges = customizableEdges63;
            guna2CustomCheckBox14.Location = new Point(289, 82);
            guna2CustomCheckBox14.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox14.Name = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox14.ShadowDecoration.CustomizableEdges = customizableEdges64;
            guna2CustomCheckBox14.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox14.Size = new Size(23, 23);
            guna2CustomCheckBox14.TabIndex = 454;
            guna2CustomCheckBox14.Text = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox14.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox14.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2CustomCheckBox15
            //
            guna2CustomCheckBox15.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox15.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox15.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox15.CustomizableEdges = customizableEdges65;
            guna2CustomCheckBox15.Location = new Point(289, 141);
            guna2CustomCheckBox15.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox15.Name = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox15.ShadowDecoration.CustomizableEdges = customizableEdges66;
            guna2CustomCheckBox15.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox15.Size = new Size(23, 23);
            guna2CustomCheckBox15.TabIndex = 457;
            guna2CustomCheckBox15.Text = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox15.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox15.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox15.Click += guna2CustomCheckBox15_Click;
            //
            // guna2CustomCheckBox16
            //
            guna2CustomCheckBox16.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox16.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox16.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox16.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox16.CustomizableEdges = customizableEdges67;
            guna2CustomCheckBox16.Location = new Point(289, 198);
            guna2CustomCheckBox16.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox16.Name = "guna2CustomCheckBox16";
            guna2CustomCheckBox16.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox16.ShadowDecoration.CustomizableEdges = customizableEdges68;
            guna2CustomCheckBox16.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox16.Size = new Size(23, 23);
            guna2CustomCheckBox16.TabIndex = 456;
            guna2CustomCheckBox16.Text = "guna2CustomCheckBox16";
            guna2CustomCheckBox16.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox16.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox16.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox16.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2CustomCheckBox17
            //
            guna2CustomCheckBox17.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox17.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox17.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox17.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox17.CustomizableEdges = customizableEdges69;
            guna2CustomCheckBox17.Location = new Point(289, 256);
            guna2CustomCheckBox17.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox17.Name = "guna2CustomCheckBox17";
            guna2CustomCheckBox17.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox17.ShadowDecoration.CustomizableEdges = customizableEdges70;
            guna2CustomCheckBox17.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox17.Size = new Size(23, 23);
            guna2CustomCheckBox17.TabIndex = 455;
            guna2CustomCheckBox17.Text = "guna2CustomCheckBox17";
            guna2CustomCheckBox17.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox17.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox17.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox17.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            //
            // guna2Button6
            //
            guna2Button6.CustomizableEdges = customizableEdges71;
            guna2Button6.DisabledState.BorderColor = Color.DarkGray;
            guna2Button6.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button6.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button6.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button6.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button6.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button6.ForeColor = Color.DimGray;
            guna2Button6.Image = (Image)resources.GetObject("guna2Button6.Image");
            guna2Button6.Location = new Point(163, 201);
            guna2Button6.Margin = new Padding(4, 3, 4, 3);
            guna2Button6.Name = "guna2Button6";
            guna2Button6.ShadowDecoration.Color = Color.DimGray;
            guna2Button6.ShadowDecoration.CustomizableEdges = customizableEdges72;
            guna2Button6.ShadowDecoration.Enabled = true;
            guna2Button6.Size = new Size(93, 21);
            guna2Button6.TabIndex = 453;
            guna2Button6.Text = "None";
            //
            // guna2Button7
            //
            guna2Button7.CustomizableEdges = customizableEdges73;
            guna2Button7.DisabledState.BorderColor = Color.DarkGray;
            guna2Button7.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button7.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button7.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button7.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button7.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button7.ForeColor = Color.DimGray;
            guna2Button7.Image = (Image)resources.GetObject("guna2Button7.Image");
            guna2Button7.Location = new Point(163, 141);
            guna2Button7.Margin = new Padding(4, 3, 4, 3);
            guna2Button7.Name = "guna2Button7";
            guna2Button7.ShadowDecoration.Color = Color.DimGray;
            guna2Button7.ShadowDecoration.CustomizableEdges = customizableEdges74;
            guna2Button7.ShadowDecoration.Enabled = true;
            guna2Button7.Size = new Size(93, 21);
            guna2Button7.TabIndex = 452;
            guna2Button7.Text = "None";
            guna2Button7.Click += guna2Button7_Click;
            //
            // guna2Button8
            //
            guna2Button8.CustomizableEdges = customizableEdges75;
            guna2Button8.DisabledState.BorderColor = Color.DarkGray;
            guna2Button8.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button8.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button8.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button8.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button8.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button8.ForeColor = Color.DimGray;
            guna2Button8.Image = (Image)resources.GetObject("guna2Button8.Image");
            guna2Button8.Location = new Point(163, 82);
            guna2Button8.Margin = new Padding(4, 3, 4, 3);
            guna2Button8.Name = "guna2Button8";
            guna2Button8.ShadowDecoration.Color = Color.DimGray;
            guna2Button8.ShadowDecoration.CustomizableEdges = customizableEdges76;
            guna2Button8.ShadowDecoration.Enabled = true;
            guna2Button8.Size = new Size(93, 21);
            guna2Button8.TabIndex = 451;
            guna2Button8.Text = "None";
            //
            // label51
            //
            label51.AutoSize = true;
            label51.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label51.ForeColor = Color.Gray;
            label51.Location = new Point(31, 273);
            label51.Margin = new Padding(2, 0, 2, 0);
            label51.Name = "label51";
            label51.Size = new Size(57, 13);
            label51.TabIndex = 449;
            label51.Text = "In Training";
            //
            // label52
            //
            label52.AutoSize = true;
            label52.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label52.ForeColor = Color.Gray;
            label52.Location = new Point(30, 250);
            label52.Margin = new Padding(2, 0, 2, 0);
            label52.Name = "label52";
            label52.Size = new Size(71, 18);
            label52.TabIndex = 450;
            label52.Text = "Magic v2";
            //
            // label53
            //
            label53.AutoSize = true;
            label53.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label53.ForeColor = Color.Gray;
            label53.Location = new Point(31, 216);
            label53.Margin = new Padding(2, 0, 2, 0);
            label53.Name = "label53";
            label53.Size = new Size(47, 13);
            label53.TabIndex = 447;
            label53.Text = "In Game";
            //
            // label54
            //
            label54.AutoSize = true;
            label54.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label54.ForeColor = Color.Gray;
            label54.Location = new Point(30, 193);
            label54.Margin = new Padding(2, 0, 2, 0);
            label54.Name = "label54";
            label54.Size = new Size(98, 18);
            label54.TabIndex = 448;
            label54.Text = "Sped - Timer";
            //
            // label55
            //
            label55.AutoSize = true;
            label55.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label55.ForeColor = Color.Gray;
            label55.Location = new Point(31, 156);
            label55.Margin = new Padding(2, 0, 2, 0);
            label55.Name = "label55";
            label55.Size = new Size(36, 13);
            label55.TabIndex = 445;
            label55.Text = "Lobby";
            //
            // label56
            //
            label56.AutoSize = true;
            label56.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label56.ForeColor = Color.Gray;
            label56.Location = new Point(30, 133);
            label56.Margin = new Padding(2, 0, 2, 0);
            label56.Name = "label56";
            label56.Size = new Size(98, 18);
            label56.TabIndex = 446;
            label56.Text = "Ghost - Hack";
            //
            // label57
            //
            label57.AutoSize = true;
            label57.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label57.ForeColor = Color.Gray;
            label57.Location = new Point(33, 99);
            label57.Margin = new Padding(2, 0, 2, 0);
            label57.Name = "label57";
            label57.Size = new Size(36, 13);
            label57.TabIndex = 443;
            label57.Text = "Lobby";
            //
            // label58
            //
            label58.AutoSize = true;
            label58.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label58.ForeColor = Color.Gray;
            label58.Location = new Point(31, 76);
            label58.Margin = new Padding(2, 0, 2, 0);
            label58.Name = "label58";
            label58.Size = new Size(86, 18);
            label58.TabIndex = 444;
            label58.Text = "Wall - Hack";
            //
            // guna2Panel18
            //
            guna2Panel18.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel18.Controls.Add(guna2PictureBox7);
            guna2Panel18.Controls.Add(label7);
            guna2Panel18.CustomizableEdges = customizableEdges79;
            guna2Panel18.Location = new Point(2, 3);
            guna2Panel18.Margin = new Padding(4, 3, 4, 3);
            guna2Panel18.Name = "guna2Panel18";
            guna2Panel18.ShadowDecoration.CustomizableEdges = customizableEdges80;
            guna2Panel18.Size = new Size(346, 35);
            guna2Panel18.TabIndex = 1;
            //
            // guna2PictureBox7
            //
            guna2PictureBox7.CustomizableEdges = customizableEdges77;
            guna2PictureBox7.Image = (Image)resources.GetObject("guna2PictureBox7.Image");
            guna2PictureBox7.ImageRotate = 0F;
            guna2PictureBox7.Location = new Point(61, 5);
            guna2PictureBox7.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox7.Name = "guna2PictureBox7";
            guna2PictureBox7.ShadowDecoration.CustomizableEdges = customizableEdges78;
            guna2PictureBox7.Size = new Size(55, 27);
            guna2PictureBox7.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox7.TabIndex = 447;
            guna2PictureBox7.TabStop = false;
            guna2PictureBox7.Click += guna2PictureBox7_Click;
            //
            // label7
            //
            label7.AutoSize = true;
            label7.BackColor = Color.Transparent;
            label7.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label7.ForeColor = Color.White;
            label7.Location = new Point(118, 3);
            label7.Margin = new Padding(4, 0, 4, 0);
            label7.Name = "label7";
            label7.Size = new Size(85, 24);
            label7.TabIndex = 424;
            label7.Text = "Memory";
            //
            // guna2Panel19
            //
            guna2Panel19.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel19.Controls.Add(guna2CustomCheckBox19);
            guna2Panel19.Controls.Add(guna2CustomCheckBox18);
            guna2Panel19.Controls.Add(guna2Panel26);
            guna2Panel19.Controls.Add(guna2Button5);
            guna2Panel19.Controls.Add(label46);
            guna2Panel19.Controls.Add(label47);
            guna2Panel19.Controls.Add(label44);
            guna2Panel19.Controls.Add(label45);
            guna2Panel19.Controls.Add(label42);
            guna2Panel19.Controls.Add(label43);
            guna2Panel19.Controls.Add(label35);
            guna2Panel19.Controls.Add(label20);
            guna2Panel19.Controls.Add(pictureBox6);
            guna2Panel19.Controls.Add(guna2Panel20);
            guna2Panel19.CustomizableEdges = customizableEdges95;
            guna2Panel19.ForeColor = Color.White;
            guna2Panel19.Location = new Point(377, 23);
            guna2Panel19.Margin = new Padding(4, 3, 4, 3);
            guna2Panel19.Name = "guna2Panel19";
            guna2Panel19.ShadowDecoration.CustomizableEdges = customizableEdges96;
            guna2Panel19.Size = new Size(350, 452);
            guna2Panel19.TabIndex = 439;
            guna2Panel19.Paint += guna2Panel19_Paint;
            //
            // guna2CustomCheckBox19
            //
            guna2CustomCheckBox19.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox19.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox19.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox19.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox19.CustomizableEdges = customizableEdges83;
            guna2CustomCheckBox19.Location = new Point(288, 204);
            guna2CustomCheckBox19.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox19.Name = "guna2CustomCheckBox19";
            guna2CustomCheckBox19.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox19.ShadowDecoration.CustomizableEdges = customizableEdges84;
            guna2CustomCheckBox19.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox19.Size = new Size(23, 23);
            guna2CustomCheckBox19.TabIndex = 461;
            guna2CustomCheckBox19.Text = "guna2CustomCheckBox19";
            guna2CustomCheckBox19.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox19.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox19.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox19.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox19.Click += guna2CustomCheckBox19_Click;
            //
            // guna2CustomCheckBox18
            //
            guna2CustomCheckBox18.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox18.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox18.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox18.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox18.CustomizableEdges = customizableEdges85;
            guna2CustomCheckBox18.Location = new Point(288, 145);
            guna2CustomCheckBox18.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox18.Name = "guna2CustomCheckBox18";
            guna2CustomCheckBox18.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox18.ShadowDecoration.CustomizableEdges = customizableEdges86;
            guna2CustomCheckBox18.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox18.Size = new Size(23, 23);
            guna2CustomCheckBox18.TabIndex = 460;
            guna2CustomCheckBox18.Text = "guna2CustomCheckBox18";
            guna2CustomCheckBox18.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox18.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox18.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox18.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox18.Click += guna2CustomCheckBox18_Click;
            //
            // guna2Panel26
            //
            guna2Panel26.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel26.Controls.Add(label49);
            guna2Panel26.Controls.Add(label48);
            guna2Panel26.CustomizableEdges = customizableEdges87;
            guna2Panel26.Location = new Point(180, 85);
            guna2Panel26.Margin = new Padding(4, 3, 4, 3);
            guna2Panel26.Name = "guna2Panel26";
            guna2Panel26.ShadowDecoration.CustomizableEdges = customizableEdges88;
            guna2Panel26.Size = new Size(97, 24);
            guna2Panel26.TabIndex = 2;
            //
            // label49
            //
            label49.AutoSize = true;
            label49.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label49.ForeColor = Color.Gray;
            label49.Location = new Point(13, 5);
            label49.Margin = new Padding(2, 0, 2, 0);
            label49.Name = "label49";
            label49.Size = new Size(38, 13);
            label49.TabIndex = 441;
            label49.Text = "#FFFF";
            //
            // label48
            //
            label48.AutoSize = true;
            label48.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label48.ForeColor = Color.Gray;
            label48.Location = new Point(-33, 5);
            label48.Margin = new Padding(2, 0, 2, 0);
            label48.Name = "label48";
            label48.Size = new Size(14, 13);
            label48.TabIndex = 419;
            label48.Text = "1";
            //
            // guna2Button5
            //
            guna2Button5.CustomizableEdges = customizableEdges89;
            guna2Button5.DisabledState.BorderColor = Color.DarkGray;
            guna2Button5.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button5.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button5.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button5.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button5.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button5.ForeColor = Color.DimGray;
            guna2Button5.Image = (Image)resources.GetObject("guna2Button5.Image");
            guna2Button5.Location = new Point(211, 261);
            guna2Button5.Margin = new Padding(4, 3, 4, 3);
            guna2Button5.Name = "guna2Button5";
            guna2Button5.ShadowDecoration.Color = Color.DimGray;
            guna2Button5.ShadowDecoration.CustomizableEdges = customizableEdges90;
            guna2Button5.ShadowDecoration.Enabled = true;
            guna2Button5.Size = new Size(93, 21);
            guna2Button5.TabIndex = 445;
            guna2Button5.Text = "None";
            guna2Button5.Click += guna2Button5_Click;
            //
            // label46
            //
            label46.AutoSize = true;
            label46.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label46.ForeColor = Color.Gray;
            label46.Location = new Point(30, 219);
            label46.Margin = new Padding(2, 0, 2, 0);
            label46.Name = "label46";
            label46.Size = new Size(80, 13);
            label46.TabIndex = 443;
            label46.Text = "On/Of Particels";
            //
            // label47
            //
            label47.AutoSize = true;
            label47.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label47.ForeColor = Color.Gray;
            label47.Location = new Point(29, 196);
            label47.Margin = new Padding(2, 0, 2, 0);
            label47.Name = "label47";
            label47.Size = new Size(69, 18);
            label47.TabIndex = 444;
            label47.Text = "Particels";
            //
            // label44
            //
            label44.AutoSize = true;
            label44.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label44.ForeColor = Color.Gray;
            label44.Location = new Point(29, 166);
            label44.Margin = new Padding(2, 0, 2, 0);
            label44.Name = "label44";
            label44.Size = new Size(65, 13);
            label44.TabIndex = 440;
            label44.Text = "Obs Discord";
            //
            // label45
            //
            label45.AutoSize = true;
            label45.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label45.ForeColor = Color.Gray;
            label45.Location = new Point(28, 143);
            label45.Margin = new Padding(2, 0, 2, 0);
            label45.Name = "label45";
            label45.Size = new Size(94, 18);
            label45.TabIndex = 441;
            label45.Text = "Mod Stream";
            //
            // label42
            //
            label42.AutoSize = true;
            label42.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label42.ForeColor = Color.Gray;
            label42.Location = new Point(34, 280);
            label42.Margin = new Padding(2, 0, 2, 0);
            label42.Name = "label42";
            label42.Size = new Size(58, 13);
            label42.TabIndex = 431;
            label42.Text = "Slecet Key";
            //
            // label43
            //
            label43.AutoSize = true;
            label43.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label43.ForeColor = Color.Gray;
            label43.Location = new Point(33, 257);
            label43.Margin = new Padding(2, 0, 2, 0);
            label43.Name = "label43";
            label43.Size = new Size(116, 18);
            label43.TabIndex = 432;
            label43.Text = "Hide Panel Key";
            //
            // label35
            //
            label35.AutoSize = true;
            label35.Font = new Font("Arial", 8.25F, FontStyle.Regular, GraphicsUnit.Point);
            label35.ForeColor = Color.Gray;
            label35.Location = new Point(20, 96);
            label35.Margin = new Padding(2, 0, 2, 0);
            label35.Name = "label35";
            label35.Size = new Size(114, 14);
            label35.TabIndex = 418;
            label35.Text = "Chsnge Color of panel";
            //
            // label20
            //
            label20.AutoSize = true;
            label20.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label20.ForeColor = Color.Gray;
            label20.Location = new Point(16, 69);
            label20.Margin = new Padding(2, 0, 2, 0);
            label20.Name = "label20";
            label20.Size = new Size(101, 18);
            label20.TabIndex = 417;
            label20.Text = "Change color";
            //
            // pictureBox6
            //
            pictureBox6.BackColor = Color.White;
            pictureBox6.Location = new Point(284, 87);
            pictureBox6.Margin = new Padding(4, 3, 4, 3);
            pictureBox6.Name = "pictureBox6";
            pictureBox6.Size = new Size(31, 22);
            pictureBox6.TabIndex = 412;
            pictureBox6.TabStop = false;
            pictureBox6.Click += pictureBox6_Click;
            //
            // guna2Panel20
            //
            guna2Panel20.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel20.Controls.Add(guna2PictureBox6);
            guna2Panel20.Controls.Add(label8);
            guna2Panel20.CustomizableEdges = customizableEdges93;
            guna2Panel20.Location = new Point(20, 3);
            guna2Panel20.Margin = new Padding(4, 3, 4, 3);
            guna2Panel20.Name = "guna2Panel20";
            guna2Panel20.ShadowDecoration.CustomizableEdges = customizableEdges94;
            guna2Panel20.Size = new Size(346, 35);
            guna2Panel20.TabIndex = 0;
            //
            // guna2PictureBox6
            //
            guna2PictureBox6.CustomizableEdges = customizableEdges91;
            guna2PictureBox6.Image = (Image)resources.GetObject("guna2PictureBox6.Image");
            guna2PictureBox6.ImageRotate = 0F;
            guna2PictureBox6.Location = new Point(65, 5);
            guna2PictureBox6.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox6.Name = "guna2PictureBox6";
            guna2PictureBox6.ShadowDecoration.CustomizableEdges = customizableEdges92;
            guna2PictureBox6.Size = new Size(55, 27);
            guna2PictureBox6.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox6.TabIndex = 447;
            guna2PictureBox6.TabStop = false;
            //
            // label8
            //
            label8.AutoSize = true;
            label8.BackColor = Color.Transparent;
            label8.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label8.Location = new Point(119, 5);
            label8.Margin = new Padding(4, 0, 4, 0);
            label8.Name = "label8";
            label8.Size = new Size(84, 24);
            label8.TabIndex = 423;
            label8.Text = "Settings";
            //
            // label22
            //
            label22.AutoSize = true;
            label22.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label22.ForeColor = Color.Gray;
            label22.Location = new Point(26, 160);
            label22.Margin = new Padding(2, 0, 2, 0);
            label22.Name = "label22";
            label22.Size = new Size(36, 13);
            label22.TabIndex = 431;
            label22.Text = "Lobby";
            //
            // label23
            //
            label23.AutoSize = true;
            label23.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label23.ForeColor = Color.Gray;
            label23.Location = new Point(24, 137);
            label23.Margin = new Padding(2, 0, 2, 0);
            label23.Name = "label23";
            label23.Size = new Size(82, 18);
            label23.TabIndex = 432;
            label23.Text = "Awm - Fov";
            //
            // label24
            //
            label24.AutoSize = true;
            label24.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label24.ForeColor = Color.Gray;
            label24.Location = new Point(27, 99);
            label24.Margin = new Padding(2, 0, 2, 0);
            label24.Name = "label24";
            label24.Size = new Size(36, 13);
            label24.TabIndex = 428;
            label24.Text = "Lobby";
            //
            // label25
            //
            label25.AutoSize = true;
            label25.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label25.ForeColor = Color.Gray;
            label25.Location = new Point(26, 76);
            label25.Margin = new Padding(2, 0, 2, 0);
            label25.Name = "label25";
            label25.Size = new Size(93, 18);
            label25.TabIndex = 429;
            label25.Text = "Awm - Scop";
            //
            // guna2Panel8
            //
            guna2Panel8.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel8.Controls.Add(guna2PictureBox2);
            guna2Panel8.Controls.Add(label3);
            guna2Panel8.CustomizableEdges = customizableEdges45;
            guna2Panel8.Location = new Point(4, 3);
            guna2Panel8.Margin = new Padding(4, 3, 4, 3);
            guna2Panel8.Name = "guna2Panel8";
            guna2Panel8.ShadowDecoration.CustomizableEdges = customizableEdges46;
            guna2Panel8.Size = new Size(346, 35);
            guna2Panel8.TabIndex = 0;
            //
            // guna2PictureBox2
            //
            guna2PictureBox2.CustomizableEdges = customizableEdges43;
            guna2PictureBox2.Image = (Image)resources.GetObject("guna2PictureBox2.Image");
            guna2PictureBox2.ImageRotate = 0F;
            guna2PictureBox2.Location = new Point(66, 5);
            guna2PictureBox2.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox2.Name = "guna2PictureBox2";
            guna2PictureBox2.ShadowDecoration.CustomizableEdges = customizableEdges44;
            guna2PictureBox2.Size = new Size(55, 27);
            guna2PictureBox2.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox2.TabIndex = 446;
            guna2PictureBox2.TabStop = false;
            //
            // label3
            //
            label3.AutoSize = true;
            label3.BackColor = Color.Transparent;
            label3.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label3.Location = new Point(124, 5);
            label3.Margin = new Padding(4, 0, 4, 0);
            label3.Name = "label3";
            label3.Size = new Size(88, 24);
            label3.TabIndex = 423;
            label3.Text = "Weapon";
            //
            // guna2ProgressBar3
            //
            guna2ProgressBar3.CustomizableEdges = customizableEdges51;
            guna2ProgressBar3.FillColor = Color.White;
            guna2ProgressBar3.Location = new Point(70, 246);
            guna2ProgressBar3.Margin = new Padding(4, 3, 4, 3);
            guna2ProgressBar3.Name = "guna2ProgressBar3";
            guna2ProgressBar3.ShadowDecoration.Color = Color.White;
            guna2ProgressBar3.ShadowDecoration.CustomizableEdges = customizableEdges52;
            guna2ProgressBar3.ShadowDecoration.Depth = 50;
            guna2ProgressBar3.ShadowDecoration.Enabled = true;
            guna2ProgressBar3.Size = new Size(2, 46);
            guna2ProgressBar3.TabIndex = 439;
            guna2ProgressBar3.Text = "guna2ProgressBar3";
            guna2ProgressBar3.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
            //
            // guna2ProgressBar2
            //
            guna2ProgressBar2.CustomizableEdges = customizableEdges53;
            guna2ProgressBar2.FillColor = Color.White;
            guna2ProgressBar2.Location = new Point(69, 163);
            guna2ProgressBar2.Margin = new Padding(4, 3, 4, 3);
            guna2ProgressBar2.Name = "guna2ProgressBar2";
            guna2ProgressBar2.ShadowDecoration.Color = Color.White;
            guna2ProgressBar2.ShadowDecoration.CustomizableEdges = customizableEdges54;
            guna2ProgressBar2.ShadowDecoration.Depth = 50;
            guna2ProgressBar2.ShadowDecoration.Enabled = true;
            guna2ProgressBar2.Size = new Size(2, 46);
            guna2ProgressBar2.TabIndex = 440;
            guna2ProgressBar2.Text = "guna2ProgressBar2";
            guna2ProgressBar2.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
            //
            // guna2ProgressBar1
            //
            guna2ProgressBar1.CustomizableEdges = customizableEdges55;
            guna2ProgressBar1.FillColor = Color.White;
            guna2ProgressBar1.Location = new Point(70, 81);
            guna2ProgressBar1.Margin = new Padding(4, 3, 4, 3);
            guna2ProgressBar1.Name = "guna2ProgressBar1";
            guna2ProgressBar1.ShadowDecoration.Color = Color.White;
            guna2ProgressBar1.ShadowDecoration.CustomizableEdges = customizableEdges56;
            guna2ProgressBar1.ShadowDecoration.Depth = 50;
            guna2ProgressBar1.ShadowDecoration.Enabled = true;
            guna2ProgressBar1.Size = new Size(2, 46);
            guna2ProgressBar1.TabIndex = 438;
            guna2ProgressBar1.Text = "guna2ProgressBar1";
            guna2ProgressBar1.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
            //
            // guna2Panel4
            //
            guna2Panel4.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel4.BorderColor = Color.FromArgb(12, 12, 12);
            guna2Panel4.Controls.Add(guna2PictureBox1);
            guna2Panel4.Controls.Add(guna2Button15);
            guna2Panel4.Controls.Add(guna2Button1);
            guna2Panel4.Controls.Add(guna2Button2);
            guna2Panel4.CustomizableEdges = customizableEdges59;
            guna2Panel4.Location = new Point(7, -24);
            guna2Panel4.Margin = new Padding(4, 3, 4, 3);
            guna2Panel4.Name = "guna2Panel4";
            guna2Panel4.ShadowDecoration.CustomizableEdges = customizableEdges60;
            guna2Panel4.Size = new Size(59, 550);
            guna2Panel4.TabIndex = 437;
            guna2Panel4.Paint += guna2Panel4_Paint;
            //
            // guna2PictureBox1
            //
            guna2PictureBox1.CustomizableEdges = customizableEdges57;
            guna2PictureBox1.Image = (Image)resources.GetObject("guna2PictureBox1.Image");
            guna2PictureBox1.ImageRotate = 0F;
            guna2PictureBox1.Location = new Point(1, 28);
            guna2PictureBox1.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox1.Name = "guna2PictureBox1";
            guna2PictureBox1.ShadowDecoration.CustomizableEdges = customizableEdges58;
            guna2PictureBox1.Size = new Size(55, 35);
            guna2PictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox1.TabIndex = 445;
            guna2PictureBox1.TabStop = false;
            guna2PictureBox1.Click += guna2PictureBox1_Click;
            //
            // guna2Panel12
            //
            guna2Panel12.BackColor = Color.FromArgb(12, 12, 12);
            guna2Panel12.Controls.Add(guna2Panel11);
            guna2Panel12.Controls.Add(guna2Panel14);
            guna2Panel12.CustomizableEdges = customizableEdges137;
            guna2Panel12.Location = new Point(-1, 877);
            guna2Panel12.Margin = new Padding(4, 3, 4, 3);
            guna2Panel12.Name = "guna2Panel12";
            guna2Panel12.ShadowDecoration.CustomizableEdges = customizableEdges138;
            guna2Panel12.Size = new Size(756, 486);
            guna2Panel12.TabIndex = 441;
            guna2Panel12.Paint += guna2Panel12_Paint;
            //
            // guna2Panel11
            //
            guna2Panel11.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel11.Controls.Add(guna2PictureBox11);
            guna2Panel11.Controls.Add(guna2PictureBox10);
            guna2Panel11.Controls.Add(guna2PictureBox9);
            guna2Panel11.Controls.Add(guna2PictureBox8);
            guna2Panel11.Controls.Add(guna2CustomCheckBox9);
            guna2Panel11.Controls.Add(guna2CustomCheckBox10);
            guna2Panel11.Controls.Add(guna2CustomCheckBox11);
            guna2Panel11.Controls.Add(guna2CustomCheckBox12);
            guna2Panel11.Controls.Add(guna2ComboBox1);
            guna2Panel11.Controls.Add(label29);
            guna2Panel11.Controls.Add(label30);
            guna2Panel11.Controls.Add(label31);
            guna2Panel11.Controls.Add(label32);
            guna2Panel11.Controls.Add(label33);
            guna2Panel11.Controls.Add(label34);
            guna2Panel11.Controls.Add(label36);
            guna2Panel11.Controls.Add(label37);
            guna2Panel11.Controls.Add(guna2Panel13);
            guna2Panel11.CustomizableEdges = customizableEdges121;
            guna2Panel11.Location = new Point(24, 23);
            guna2Panel11.Margin = new Padding(4, 3, 4, 3);
            guna2Panel11.Name = "guna2Panel11";
            guna2Panel11.ShadowDecoration.CustomizableEdges = customizableEdges122;
            guna2Panel11.Size = new Size(350, 452);
            guna2Panel11.TabIndex = 438;
            guna2Panel11.Paint += guna2Panel11_Paint;
            //
            // guna2PictureBox11
            //
            guna2PictureBox11.CustomizableEdges = customizableEdges99;
            guna2PictureBox11.Image = (Image)resources.GetObject("guna2PictureBox11.Image");
            guna2PictureBox11.ImageRotate = 0F;
            guna2PictureBox11.Location = new Point(292, 267);
            guna2PictureBox11.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox11.Name = "guna2PictureBox11";
            guna2PictureBox11.ShadowDecoration.CustomizableEdges = customizableEdges100;
            guna2PictureBox11.Size = new Size(55, 27);
            guna2PictureBox11.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox11.TabIndex = 463;
            guna2PictureBox11.TabStop = false;
            guna2PictureBox11.Click += guna2PictureBox11_Click;
            //
            // guna2PictureBox10
            //
            guna2PictureBox10.CustomizableEdges = customizableEdges101;
            guna2PictureBox10.Image = (Image)resources.GetObject("guna2PictureBox10.Image");
            guna2PictureBox10.ImageRotate = 0F;
            guna2PictureBox10.Location = new Point(291, 204);
            guna2PictureBox10.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox10.Name = "guna2PictureBox10";
            guna2PictureBox10.ShadowDecoration.CustomizableEdges = customizableEdges102;
            guna2PictureBox10.Size = new Size(55, 27);
            guna2PictureBox10.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox10.TabIndex = 462;
            guna2PictureBox10.TabStop = false;
            guna2PictureBox10.Click += guna2PictureBox10_Click;
            //
            // guna2PictureBox9
            //
            guna2PictureBox9.CustomizableEdges = customizableEdges103;
            guna2PictureBox9.Image = (Image)resources.GetObject("guna2PictureBox9.Image");
            guna2PictureBox9.ImageRotate = 0F;
            guna2PictureBox9.Location = new Point(292, 144);
            guna2PictureBox9.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox9.Name = "guna2PictureBox9";
            guna2PictureBox9.ShadowDecoration.CustomizableEdges = customizableEdges104;
            guna2PictureBox9.Size = new Size(55, 27);
            guna2PictureBox9.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox9.TabIndex = 461;
            guna2PictureBox9.TabStop = false;
            guna2PictureBox9.Click += guna2PictureBox9_Click;
            //
            // guna2PictureBox8
            //
            guna2PictureBox8.CustomizableEdges = customizableEdges105;
            guna2PictureBox8.Image = (Image)resources.GetObject("guna2PictureBox8.Image");
            guna2PictureBox8.ImageRotate = 0F;
            guna2PictureBox8.Location = new Point(292, 85);
            guna2PictureBox8.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox8.Name = "guna2PictureBox8";
            guna2PictureBox8.ShadowDecoration.CustomizableEdges = customizableEdges106;
            guna2PictureBox8.Size = new Size(55, 27);
            guna2PictureBox8.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox8.TabIndex = 460;
            guna2PictureBox8.TabStop = false;
            guna2PictureBox8.Click += guna2PictureBox8_Click;
            //
            // guna2CustomCheckBox9
            //
            guna2CustomCheckBox9.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox9.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox9.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox9.CustomizableEdges = customizableEdges107;
            guna2CustomCheckBox9.Location = new Point(261, 88);
            guna2CustomCheckBox9.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox9.Name = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox9.ShadowDecoration.CustomizableEdges = customizableEdges108;
            guna2CustomCheckBox9.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox9.Size = new Size(23, 23);
            guna2CustomCheckBox9.TabIndex = 456;
            guna2CustomCheckBox9.Text = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox9.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox9.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox9.Click += guna2CustomCheckBox9_Click;
            //
            // guna2CustomCheckBox10
            //
            guna2CustomCheckBox10.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox10.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox10.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox10.CustomizableEdges = customizableEdges109;
            guna2CustomCheckBox10.Location = new Point(261, 148);
            guna2CustomCheckBox10.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox10.Name = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox10.ShadowDecoration.CustomizableEdges = customizableEdges110;
            guna2CustomCheckBox10.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox10.Size = new Size(23, 23);
            guna2CustomCheckBox10.TabIndex = 459;
            guna2CustomCheckBox10.Text = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox10.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox10.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox10.Click += guna2CustomCheckBox10_Click;
            //
            // guna2CustomCheckBox11
            //
            guna2CustomCheckBox11.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox11.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox11.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox11.CustomizableEdges = customizableEdges111;
            guna2CustomCheckBox11.Location = new Point(261, 208);
            guna2CustomCheckBox11.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox11.Name = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox11.ShadowDecoration.CustomizableEdges = customizableEdges112;
            guna2CustomCheckBox11.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox11.Size = new Size(23, 23);
            guna2CustomCheckBox11.TabIndex = 458;
            guna2CustomCheckBox11.Text = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox11.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox11.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox11.Click += guna2CustomCheckBox11_Click;
            //
            // guna2CustomCheckBox12
            //
            guna2CustomCheckBox12.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox12.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox12.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox12.CustomizableEdges = customizableEdges113;
            guna2CustomCheckBox12.Location = new Point(261, 269);
            guna2CustomCheckBox12.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox12.Name = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox12.ShadowDecoration.CustomizableEdges = customizableEdges114;
            guna2CustomCheckBox12.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox12.Size = new Size(23, 23);
            guna2CustomCheckBox12.TabIndex = 457;
            guna2CustomCheckBox12.Text = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox12.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox12.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox12.Click += guna2CustomCheckBox12_Click;
            //
            // guna2ComboBox1
            //
            guna2ComboBox1.BackColor = Color.Transparent;
            guna2ComboBox1.BorderColor = Color.FromArgb(12, 12, 12);
            guna2ComboBox1.BorderRadius = 8;
            guna2ComboBox1.CustomizableEdges = customizableEdges115;
            guna2ComboBox1.DrawMode = DrawMode.OwnerDrawFixed;
            guna2ComboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
            guna2ComboBox1.FillColor = Color.FromArgb(12, 12, 12);
            guna2ComboBox1.FocusedColor = Color.FromArgb(224, 224, 224);
            guna2ComboBox1.FocusedState.BorderColor = Color.FromArgb(224, 224, 224);
            guna2ComboBox1.Font = new Font("Microsoft Sans Serif", 9.75F, FontStyle.Regular, GraphicsUnit.Point);
            guna2ComboBox1.ForeColor = Color.White;
            guna2ComboBox1.ItemHeight = 15;
            guna2ComboBox1.Items.AddRange(new object[] { "Top", "Bottom", "Center" });
            guna2ComboBox1.Location = new Point(128, 337);
            guna2ComboBox1.Margin = new Padding(5, 6, 5, 6);
            guna2ComboBox1.Name = "guna2ComboBox1";
            guna2ComboBox1.ShadowDecoration.CustomizableEdges = customizableEdges116;
            guna2ComboBox1.Size = new Size(107, 21);
            guna2ComboBox1.StartIndex = 0;
            guna2ComboBox1.TabIndex = 455;
            guna2ComboBox1.SelectedIndexChanged += guna2ComboBox1_SelectedIndexChanged;
            //
            // label29
            //
            label29.AutoSize = true;
            label29.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label29.ForeColor = Color.Gray;
            label29.Location = new Point(24, 288);
            label29.Margin = new Padding(2, 0, 2, 0);
            label29.Name = "label29";
            label29.Size = new Size(47, 13);
            label29.TabIndex = 449;
            label29.Text = "In Game";
            //
            // label30
            //
            label30.AutoSize = true;
            label30.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label30.ForeColor = Color.Gray;
            label30.Location = new Point(23, 265);
            label30.Margin = new Padding(2, 0, 2, 0);
            label30.Name = "label30";
            label30.Size = new Size(101, 18);
            label30.TabIndex = 450;
            label30.Text = "Esp Skeleton";
            //
            // label31
            //
            label31.AutoSize = true;
            label31.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label31.ForeColor = Color.Gray;
            label31.Location = new Point(24, 225);
            label31.Margin = new Padding(2, 0, 2, 0);
            label31.Name = "label31";
            label31.Size = new Size(47, 13);
            label31.TabIndex = 446;
            label31.Text = "In Game";
            //
            // label32
            //
            label32.AutoSize = true;
            label32.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label32.ForeColor = Color.Gray;
            label32.Location = new Point(23, 202);
            label32.Margin = new Padding(2, 0, 2, 0);
            label32.Name = "label32";
            label32.Size = new Size(82, 18);
            label32.TabIndex = 447;
            label32.Text = "Esp Name";
            //
            // label33
            //
            label33.AutoSize = true;
            label33.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label33.ForeColor = Color.Gray;
            label33.Location = new Point(24, 165);
            label33.Margin = new Padding(2, 0, 2, 0);
            label33.Name = "label33";
            label33.Size = new Size(47, 13);
            label33.TabIndex = 443;
            label33.Text = "In Game";
            //
            // label34
            //
            label34.AutoSize = true;
            label34.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label34.ForeColor = Color.Gray;
            label34.Location = new Point(23, 142);
            label34.Margin = new Padding(2, 0, 2, 0);
            label34.Name = "label34";
            label34.Size = new Size(97, 18);
            label34.TabIndex = 444;
            label34.Text = "Esp Cornerd";
            //
            // label36
            //
            label36.AutoSize = true;
            label36.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label36.ForeColor = Color.Gray;
            label36.Location = new Point(26, 104);
            label36.Margin = new Padding(2, 0, 2, 0);
            label36.Name = "label36";
            label36.Size = new Size(47, 13);
            label36.TabIndex = 440;
            label36.Text = "In Game";
            //
            // label37
            //
            label37.AutoSize = true;
            label37.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label37.ForeColor = Color.Gray;
            label37.Location = new Point(24, 81);
            label37.Margin = new Padding(2, 0, 2, 0);
            label37.Name = "label37";
            label37.Size = new Size(70, 18);
            label37.TabIndex = 441;
            label37.Text = "Esp Line";
            //
            // guna2Panel13
            //
            guna2Panel13.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel13.Controls.Add(guna2PictureBox4);
            guna2Panel13.Controls.Add(label5);
            guna2Panel13.CustomizableEdges = customizableEdges119;
            guna2Panel13.Location = new Point(2, 3);
            guna2Panel13.Margin = new Padding(4, 3, 4, 3);
            guna2Panel13.Name = "guna2Panel13";
            guna2Panel13.ShadowDecoration.CustomizableEdges = customizableEdges120;
            guna2Panel13.Size = new Size(346, 35);
            guna2Panel13.TabIndex = 1;
            //
            // guna2PictureBox4
            //
            guna2PictureBox4.CustomizableEdges = customizableEdges117;
            guna2PictureBox4.Image = (Image)resources.GetObject("guna2PictureBox4.Image");
            guna2PictureBox4.ImageRotate = 0F;
            guna2PictureBox4.Location = new Point(57, 5);
            guna2PictureBox4.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox4.Name = "guna2PictureBox4";
            guna2PictureBox4.ShadowDecoration.CustomizableEdges = customizableEdges118;
            guna2PictureBox4.Size = new Size(55, 27);
            guna2PictureBox4.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox4.TabIndex = 447;
            guna2PictureBox4.TabStop = false;
            //
            // label5
            //
            label5.AutoSize = true;
            label5.BackColor = Color.Transparent;
            label5.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label5.ForeColor = Color.White;
            label5.Location = new Point(121, 3);
            label5.Margin = new Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new Size(98, 24);
            label5.TabIndex = 424;
            label5.Text = "Esp Line ";
            //
            // guna2Panel14
            //
            guna2Panel14.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel14.Controls.Add(label61);
            guna2Panel14.Controls.Add(guna2Panel22);
            guna2Panel14.Controls.Add(label60);
            guna2Panel14.Controls.Add(guna2Panel30);
            guna2Panel14.Controls.Add(label59);
            guna2Panel14.Controls.Add(guna2CustomCheckBox13);
            guna2Panel14.Controls.Add(label50);
            guna2Panel14.Controls.Add(guna2Panel24);
            guna2Panel14.Controls.Add(label41);
            guna2Panel14.Controls.Add(label38);
            guna2Panel14.Controls.Add(label39);
            guna2Panel14.Controls.Add(guna2Panel15);
            guna2Panel14.CustomizableEdges = customizableEdges135;
            guna2Panel14.ForeColor = Color.White;
            guna2Panel14.Location = new Point(390, 23);
            guna2Panel14.Margin = new Padding(4, 3, 4, 3);
            guna2Panel14.Name = "guna2Panel14";
            guna2Panel14.ShadowDecoration.CustomizableEdges = customizableEdges136;
            guna2Panel14.Size = new Size(350, 452);
            guna2Panel14.TabIndex = 439;
            guna2Panel14.Paint += guna2Panel14_Paint;
            //
            // label61
            //
            label61.AutoSize = true;
            label61.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label61.ForeColor = Color.Gray;
            label61.Location = new Point(23, 288);
            label61.Margin = new Padding(2, 0, 2, 0);
            label61.Name = "label61";
            label61.Size = new Size(112, 13);
            label61.TabIndex = 465;
            label61.Text = "Glow Border Tchiknes";
            //
            // guna2Panel22
            //
            guna2Panel22.CustomizableEdges = customizableEdges123;
            guna2Panel22.Location = new Point(26, 164);
            guna2Panel22.Margin = new Padding(4, 3, 4, 3);
            guna2Panel22.Name = "guna2Panel22";
            guna2Panel22.ShadowDecoration.Color = Color.White;
            guna2Panel22.ShadowDecoration.CustomizableEdges = customizableEdges124;
            guna2Panel22.ShadowDecoration.Depth = 50;
            guna2Panel22.ShadowDecoration.Enabled = true;
            guna2Panel22.Size = new Size(293, 6);
            guna2Panel22.TabIndex = 460;
            guna2Panel22.Paint += guna2Panel22_Paint_2;
            //
            // label60
            //
            label60.AutoSize = true;
            label60.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label60.ForeColor = Color.Gray;
            label60.Location = new Point(293, 288);
            label60.Margin = new Padding(2, 0, 2, 0);
            label60.Name = "label60";
            label60.Size = new Size(13, 13);
            label60.TabIndex = 464;
            label60.Text = "0";
            //
            // guna2Panel30
            //
            guna2Panel30.CustomizableEdges = customizableEdges125;
            guna2Panel30.Location = new Point(26, 312);
            guna2Panel30.Margin = new Padding(4, 3, 4, 3);
            guna2Panel30.Name = "guna2Panel30";
            guna2Panel30.ShadowDecoration.Color = Color.White;
            guna2Panel30.ShadowDecoration.CustomizableEdges = customizableEdges126;
            guna2Panel30.ShadowDecoration.Depth = 50;
            guna2Panel30.ShadowDecoration.Enabled = true;
            guna2Panel30.Size = new Size(293, 7);
            guna2Panel30.TabIndex = 463;
            guna2Panel30.Paint += guna2Panel30_Paint_1;
            //
            // label59
            //
            label59.AutoSize = true;
            label59.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label59.ForeColor = Color.Gray;
            label59.Location = new Point(293, 204);
            label59.Margin = new Padding(2, 0, 2, 0);
            label59.Name = "label59";
            label59.Size = new Size(13, 13);
            label59.TabIndex = 462;
            label59.Text = "0";
            //
            // guna2CustomCheckBox13
            //
            guna2CustomCheckBox13.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox13.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox13.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox13.CustomizableEdges = customizableEdges127;
            guna2CustomCheckBox13.Location = new Point(280, 85);
            guna2CustomCheckBox13.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox13.Name = "guna2CustomCheckBox13";
            guna2CustomCheckBox13.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox13.ShadowDecoration.CustomizableEdges = customizableEdges128;
            guna2CustomCheckBox13.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox13.Size = new Size(23, 23);
            guna2CustomCheckBox13.TabIndex = 461;
            guna2CustomCheckBox13.Text = "z";
            guna2CustomCheckBox13.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox13.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox13.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox13.Click += guna2CustomCheckBox13_Click;
            //
            // label50
            //
            label50.AutoSize = true;
            label50.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label50.ForeColor = Color.Gray;
            label50.Location = new Point(27, 201);
            label50.Margin = new Padding(2, 0, 2, 0);
            label50.Name = "label50";
            label50.Size = new Size(101, 13);
            label50.TabIndex = 460;
            label50.Text = "Glow Line Tchiknes";
            //
            // guna2Panel24
            //
            guna2Panel24.CustomizableEdges = customizableEdges129;
            guna2Panel24.Location = new Point(26, 226);
            guna2Panel24.Margin = new Padding(4, 3, 4, 3);
            guna2Panel24.Name = "guna2Panel24";
            guna2Panel24.ShadowDecoration.Color = Color.White;
            guna2Panel24.ShadowDecoration.CustomizableEdges = customizableEdges130;
            guna2Panel24.ShadowDecoration.Depth = 50;
            guna2Panel24.ShadowDecoration.Enabled = true;
            guna2Panel24.Size = new Size(293, 7);
            guna2Panel24.TabIndex = 459;
            guna2Panel24.Paint += guna2Panel24_Paint_1;
            //
            // label41
            //
            label41.AutoSize = true;
            label41.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label41.ForeColor = Color.Gray;
            label41.Location = new Point(22, 133);
            label41.Margin = new Padding(2, 0, 2, 0);
            label41.Name = "label41";
            label41.Size = new Size(137, 13);
            label41.TabIndex = 458;
            label41.Text = "Change Color For Glow Esp";
            //
            // label38
            //
            label38.AutoSize = true;
            label38.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label38.ForeColor = Color.Gray;
            label38.Location = new Point(22, 104);
            label38.Margin = new Padding(2, 0, 2, 0);
            label38.Name = "label38";
            label38.Size = new Size(47, 13);
            label38.TabIndex = 453;
            label38.Text = "In Game";
            //
            // label39
            //
            label39.AutoSize = true;
            label39.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Bold, GraphicsUnit.Point);
            label39.ForeColor = Color.Gray;
            label39.Location = new Point(19, 81);
            label39.Margin = new Padding(2, 0, 2, 0);
            label39.Name = "label39";
            label39.Size = new Size(85, 20);
            label39.TabIndex = 454;
            label39.Text = "Glow Esp";
            //
            // guna2Panel15
            //
            guna2Panel15.BackColor = Color.FromArgb(20, 20, 20);
            guna2Panel15.Controls.Add(guna2PictureBox5);
            guna2Panel15.Controls.Add(label6);
            guna2Panel15.CustomizableEdges = customizableEdges133;
            guna2Panel15.Location = new Point(4, 3);
            guna2Panel15.Margin = new Padding(4, 3, 4, 3);
            guna2Panel15.Name = "guna2Panel15";
            guna2Panel15.ShadowDecoration.CustomizableEdges = customizableEdges134;
            guna2Panel15.Size = new Size(346, 35);
            guna2Panel15.TabIndex = 0;
            //
            // guna2PictureBox5
            //
            guna2PictureBox5.CustomizableEdges = customizableEdges131;
            guna2PictureBox5.Image = (Image)resources.GetObject("guna2PictureBox5.Image");
            guna2PictureBox5.ImageRotate = 0F;
            guna2PictureBox5.Location = new Point(35, 5);
            guna2PictureBox5.Margin = new Padding(4, 3, 4, 3);
            guna2PictureBox5.Name = "guna2PictureBox5";
            guna2PictureBox5.ShadowDecoration.CustomizableEdges = customizableEdges132;
            guna2PictureBox5.Size = new Size(55, 27);
            guna2PictureBox5.SizeMode = PictureBoxSizeMode.Zoom;
            guna2PictureBox5.TabIndex = 447;
            guna2PictureBox5.TabStop = false;
            //
            // label6
            //
            label6.AutoSize = true;
            label6.BackColor = Color.Transparent;
            label6.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label6.Location = new Point(94, 5);
            label6.Margin = new Padding(4, 0, 4, 0);
            label6.Name = "label6";
            label6.Size = new Size(148, 24);
            label6.TabIndex = 423;
            label6.Text = "Memory Visual";
            //
            // label40
            //
            label40.AutoSize = true;
            label40.Font = new Font("Microsoft Sans Serif", 8F, FontStyle.Regular, GraphicsUnit.Point);
            label40.ForeColor = Color.Gray;
            label40.Location = new Point(1712, 654);
            label40.Margin = new Padding(2, 0, 2, 0);
            label40.Name = "label40";
            label40.Size = new Size(47, 13);
            label40.TabIndex = 457;
            label40.Text = "In Game";
            //
            // label26
            //
            label26.AutoSize = true;
            label26.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label26.ForeColor = Color.Gray;
            label26.Location = new Point(50, 23);
            label26.Margin = new Padding(2, 0, 2, 0);
            label26.Name = "label26";
            label26.Size = new Size(137, 15);
            label26.TabIndex = 430;
            label26.Text = "Enable Successfulyy";
            //
            // guna2ProgressBar4
            //
            guna2ProgressBar4.BackColor = Color.FromArgb(10, 10, 10);
            guna2ProgressBar4.BorderColor = Color.Transparent;
            guna2ProgressBar4.CustomizableEdges = customizableEdges139;
            guna2ProgressBar4.FillColor = Color.Transparent;
            guna2ProgressBar4.Location = new Point(19, 47);
            guna2ProgressBar4.Margin = new Padding(2);
            guna2ProgressBar4.Name = "guna2ProgressBar4";
            guna2ProgressBar4.ProgressColor = Color.White;
            guna2ProgressBar4.ProgressColor2 = Color.White;
            guna2ProgressBar4.ShadowDecoration.Color = Color.White;
            guna2ProgressBar4.ShadowDecoration.CustomizableEdges = customizableEdges140;
            guna2ProgressBar4.ShadowDecoration.Depth = 45;
            guna2ProgressBar4.ShadowDecoration.Enabled = true;
            guna2ProgressBar4.Size = new Size(244, 6);
            guna2ProgressBar4.TabIndex = 429;
            guna2ProgressBar4.Text = "guna2ProgressBar4";
            guna2ProgressBar4.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
            //
            // label27
            //
            label27.AutoSize = true;
            label27.Font = new Font("Microsoft Sans Serif", 12F, FontStyle.Bold, GraphicsUnit.Point);
            label27.ForeColor = Color.White;
            label27.Location = new Point(2, 1);
            label27.Margin = new Padding(2, 0, 2, 0);
            label27.Name = "label27";
            label27.Size = new Size(63, 20);
            label27.TabIndex = 428;
            label27.Text = "Niguzz";
            //
            // timer1
            //
            timer1.Interval = 40;
            timer1.Tick += timer1_Tick;
            //
            // guna2Panel23
            //
            guna2Panel23.BackColor = Color.Transparent;
            guna2Panel23.Controls.Add(guna2Panel2);
            guna2Panel23.CustomizableEdges = customizableEdges141;
            guna2Panel23.ForeColor = Color.Transparent;
            guna2Panel23.Location = new Point(1031, 81);
            guna2Panel23.Margin = new Padding(4, 3, 4, 3);
            guna2Panel23.Name = "guna2Panel23";
            guna2Panel23.ShadowDecoration.CustomizableEdges = customizableEdges142;
            guna2Panel23.Size = new Size(250, 192);
            guna2Panel23.TabIndex = 444;
            //
            // guna2Panel21
            //
            guna2Panel21.BackColor = Color.FromArgb(12, 12, 12);
            guna2Panel21.Controls.Add(guna2Panel25);
            guna2Panel21.CustomizableEdges = customizableEdges145;
            guna2Panel21.ForeColor = Color.Transparent;
            guna2Panel21.Location = new Point(1058, 324);
            guna2Panel21.Margin = new Padding(4, 3, 4, 3);
            guna2Panel21.Name = "guna2Panel21";
            guna2Panel21.ShadowDecoration.CustomizableEdges = customizableEdges146;
            guna2Panel21.Size = new Size(286, 75);
            guna2Panel21.TabIndex = 445;
            //
            // guna2Panel25
            //
            guna2Panel25.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel25.Controls.Add(label26);
            guna2Panel25.Controls.Add(guna2ProgressBar4);
            guna2Panel25.Controls.Add(label27);
            guna2Panel25.CustomizableEdges = customizableEdges143;
            guna2Panel25.FillColor = Color.FromArgb(12, 12, 12);
            guna2Panel25.ForeColor = Color.Black;
            guna2Panel25.Location = new Point(5, 5);
            guna2Panel25.Margin = new Padding(4, 3, 4, 3);
            guna2Panel25.Name = "guna2Panel25";
            guna2Panel25.ShadowDecoration.Color = Color.White;
            guna2Panel25.ShadowDecoration.CustomizableEdges = customizableEdges144;
            guna2Panel25.ShadowDecoration.Depth = 50;
            guna2Panel25.ShadowDecoration.Enabled = true;
            guna2Panel25.Size = new Size(275, 66);
            guna2Panel25.TabIndex = 421;
            guna2Panel25.Paint += guna2Panel25_Paint;
            //
            // guna2Button9
            //
            guna2Button9.CustomizableEdges = customizableEdges147;
            guna2Button9.DisabledState.BorderColor = Color.DarkGray;
            guna2Button9.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button9.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button9.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button9.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button9.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button9.ForeColor = Color.DimGray;
            guna2Button9.Location = new Point(1735, 802);
            guna2Button9.Margin = new Padding(4, 3, 4, 3);
            guna2Button9.Name = "guna2Button9";
            guna2Button9.ShadowDecoration.Color = Color.DimGray;
            guna2Button9.ShadowDecoration.CustomizableEdges = customizableEdges148;
            guna2Button9.ShadowDecoration.Enabled = true;
            guna2Button9.Size = new Size(93, 21);
            guna2Button9.TabIndex = 459;
            guna2Button9.Text = "None";
            guna2Button9.Click += guna2Button9_Click;
            //
            // guna2Panel111
            //
            guna2Panel111.BackColor = Color.FromArgb(8, 8, 8);
            guna2Panel111.Controls.Add(label1);
            guna2Panel111.Controls.Add(guna2Button11);
            guna2Panel111.CustomizableEdges = customizableEdges151;
            guna2Panel111.Location = new Point(10, 167);
            guna2Panel111.Margin = new Padding(4, 3, 4, 3);
            guna2Panel111.Name = "guna2Panel111";
            guna2Panel111.ShadowDecoration.Color = Color.White;
            guna2Panel111.ShadowDecoration.CustomizableEdges = customizableEdges152;
            guna2Panel111.Size = new Size(187, 31);
            guna2Panel111.TabIndex = 464;
            //
            // label1
            //
            label1.AutoSize = true;
            label1.ForeColor = Color.FromArgb(224, 224, 224);
            label1.Location = new Point(18, 9);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(44, 15);
            label1.TabIndex = 461;
            label1.Text = "#FFFFF";
            //
            // guna2Button11
            //
            guna2Button11.BorderColor = Color.FromArgb(8, 8, 8);
            guna2Button11.CustomizableEdges = customizableEdges149;
            guna2Button11.DisabledState.BorderColor = Color.DarkGray;
            guna2Button11.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button11.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button11.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button11.FillColor = Color.FromArgb(8, 8, 8);
            guna2Button11.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button11.ForeColor = Color.White;
            guna2Button11.Location = new Point(139, 5);
            guna2Button11.Margin = new Padding(4, 3, 4, 3);
            guna2Button11.Name = "guna2Button11";
            guna2Button11.ShadowDecoration.Color = Color.White;
            guna2Button11.ShadowDecoration.CustomizableEdges = customizableEdges150;
            guna2Button11.Size = new Size(43, 21);
            guna2Button11.TabIndex = 462;
            //
            // guna2Panel29
            //
            guna2Panel29.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel29.CustomizableEdges = customizableEdges153;
            guna2Panel29.ForeColor = Color.FromArgb(12, 12, 12);
            guna2Panel29.Location = new Point(7, 153);
            guna2Panel29.Margin = new Padding(4, 3, 4, 3);
            guna2Panel29.Name = "guna2Panel29";
            guna2Panel29.ShadowDecoration.Color = Color.White;
            guna2Panel29.ShadowDecoration.CustomizableEdges = customizableEdges154;
            guna2Panel29.ShadowDecoration.Depth = 50;
            guna2Panel29.Size = new Size(196, 7);
            guna2Panel29.TabIndex = 424;
            guna2Panel29.Paint += guna2Panel29_Paint;
            //
            // guna2Panel28
            //
            guna2Panel28.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel28.CustomizableEdges = customizableEdges155;
            guna2Panel28.ForeColor = Color.FromArgb(12, 12, 12);
            guna2Panel28.Location = new Point(5, 7);
            guna2Panel28.Margin = new Padding(4, 3, 4, 3);
            guna2Panel28.Name = "guna2Panel28";
            guna2Panel28.ShadowDecoration.Color = Color.White;
            guna2Panel28.ShadowDecoration.CustomizableEdges = customizableEdges156;
            guna2Panel28.ShadowDecoration.Depth = 50;
            guna2Panel28.Size = new Size(201, 140);
            guna2Panel28.TabIndex = 423;
            guna2Panel28.Paint += guna2Panel28_Paint;
            //
            // guna2Panel27
            //
            guna2Panel27.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel27.Controls.Add(guna2Panel111);
            guna2Panel27.Controls.Add(guna2Panel28);
            guna2Panel27.Controls.Add(guna2Panel29);
            guna2Panel27.CustomizableEdges = customizableEdges157;
            guna2Panel27.FillColor = Color.FromArgb(10, 10, 10);
            guna2Panel27.ForeColor = Color.FromArgb(10, 10, 10);
            guna2Panel27.Location = new Point(1324, 96);
            guna2Panel27.Margin = new Padding(4, 3, 4, 3);
            guna2Panel27.Name = "guna2Panel27";
            guna2Panel27.ShadowDecoration.Color = Color.White;
            guna2Panel27.ShadowDecoration.CustomizableEdges = customizableEdges158;
            guna2Panel27.ShadowDecoration.Depth = 50;
            guna2Panel27.ShadowDecoration.Enabled = true;
            guna2Panel27.Size = new Size(210, 203);
            guna2Panel27.TabIndex = 462;
            guna2Panel27.Paint += guna2Panel27_Paint;
            //
            // guna2Button12
            //
            guna2Button12.CustomizableEdges = customizableEdges159;
            guna2Button12.DisabledState.BorderColor = Color.DarkGray;
            guna2Button12.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button12.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button12.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button12.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button12.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button12.ForeColor = Color.DimGray;
            guna2Button12.Location = new Point(1725, 829);
            guna2Button12.Margin = new Padding(4, 3, 4, 3);
            guna2Button12.Name = "guna2Button12";
            guna2Button12.ShadowDecoration.Color = Color.DimGray;
            guna2Button12.ShadowDecoration.CustomizableEdges = customizableEdges160;
            guna2Button12.ShadowDecoration.Enabled = true;
            guna2Button12.Size = new Size(93, 21);
            guna2Button12.TabIndex = 463;
            guna2Button12.Text = "None";
            guna2Button12.Click += guna2Button12_Click;
            //
            // guna2Button18
            //
            guna2Button18.CustomizableEdges = customizableEdges161;
            guna2Button18.DisabledState.BorderColor = Color.DarkGray;
            guna2Button18.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button18.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button18.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button18.FillColor = Color.Transparent;
            guna2Button18.FocusedColor = Color.Transparent;
            guna2Button18.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button18.ForeColor = Color.White;
            guna2Button18.HoverState.FillColor = Color.Transparent;
            guna2Button18.ImageSize = new Size(30, 30);
            guna2Button18.Location = new Point(866, 92);
            guna2Button18.Margin = new Padding(4, 3, 4, 3);
            guna2Button18.Name = "guna2Button18";
            guna2Button18.PressedColor = Color.Transparent;
            guna2Button18.ShadowDecoration.CustomizableEdges = customizableEdges162;
            guna2Button18.Size = new Size(101, 63);
            guna2Button18.TabIndex = 464;
            guna2Button18.Click += guna2Button18_Click;
            //
            // timer2
            //
            timer2.Enabled = true;
            timer2.Interval = 1000;
            //
            // timer3
            //
            timer3.Interval = 40;
            //
            // timer4
            //
            timer4.Enabled = false;  // إيقاف Timer المدمر للـ ESP
            timer4.Interval = 1000;
            timer4.Tick += timer4_Tick;
            //
            // weaponIconPictureBox
            //
            weaponIconPictureBox.CustomizableEdges = customizableEdges163;
            weaponIconPictureBox.Image = (Image)resources.GetObject("weaponIconPictureBox.Image");
            weaponIconPictureBox.ImageRotate = 0F;
            weaponIconPictureBox.InitialImage = (Image)resources.GetObject("weaponIconPictureBox.InitialImage");
            weaponIconPictureBox.Location = new Point(433, 587);
            weaponIconPictureBox.Margin = new Padding(4, 3, 4, 3);
            weaponIconPictureBox.Name = "weaponIconPictureBox";
            weaponIconPictureBox.ShadowDecoration.CustomizableEdges = customizableEdges164;
            weaponIconPictureBox.Size = new Size(55, 27);
            weaponIconPictureBox.SizeMode = PictureBoxSizeMode.Zoom;
            weaponIconPictureBox.TabIndex = 465;
            weaponIconPictureBox.TabStop = false;
            //
            // Form5
            //
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.Black;
            ClientSize = new Size(1441, 866);
            Controls.Add(weaponIconPictureBox);
            Controls.Add(guna2Button18);
            Controls.Add(guna2Button12);
            Controls.Add(guna2Panel27);
            Controls.Add(guna2Button9);
            Controls.Add(guna2Panel21);
            Controls.Add(label40);
            Controls.Add(guna2Panel23);
            Controls.Add(guna2Panel16);
            Controls.Add(guna2Panel12);
            Controls.Add(guna2Panel10);
            Controls.Add(label2);
            FormBorderStyle = FormBorderStyle.None;
            Margin = new Padding(4, 3, 4, 3);
            Name = "Form5";
            ShowIcon = false;
            ShowInTaskbar = false;
            Text = "Esp Cornerd";
            TransparencyKey = Color.Black;
            Load += Form5_Load;
            guna2Panel2.ResumeLayout(false);
            guna2Panel10.ResumeLayout(false);
            guna2Panel10.PerformLayout();
            guna2Panel7.ResumeLayout(false);
            guna2Panel5.ResumeLayout(false);
            guna2Panel5.PerformLayout();
            guna2Panel9.ResumeLayout(false);
            guna2Panel9.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).EndInit();
            guna2Panel6.ResumeLayout(false);
            guna2Panel6.PerformLayout();
            guna2Panel16.ResumeLayout(false);
            guna2Panel17.ResumeLayout(false);
            guna2Panel17.PerformLayout();
            guna2Panel18.ResumeLayout(false);
            guna2Panel18.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).EndInit();
            guna2Panel19.ResumeLayout(false);
            guna2Panel19.PerformLayout();
            guna2Panel26.ResumeLayout(false);
            guna2Panel26.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBox6).EndInit();
            guna2Panel20.ResumeLayout(false);
            guna2Panel20.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox6).EndInit();
            guna2Panel8.ResumeLayout(false);
            guna2Panel8.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).EndInit();
            guna2Panel4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox1).EndInit();
            guna2Panel12.ResumeLayout(false);
            guna2Panel11.ResumeLayout(false);
            guna2Panel11.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox11).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox10).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox9).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox8).EndInit();
            guna2Panel13.ResumeLayout(false);
            guna2Panel13.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox4).EndInit();
            guna2Panel14.ResumeLayout(false);
            guna2Panel14.PerformLayout();
            guna2Panel15.ResumeLayout(false);
            guna2Panel15.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox5).EndInit();
            guna2Panel23.ResumeLayout(false);
            guna2Panel21.ResumeLayout(false);
            guna2Panel25.ResumeLayout(false);
            guna2Panel25.PerformLayout();
            guna2Panel111.ResumeLayout(false);
            guna2Panel111.PerformLayout();
            guna2Panel27.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)weaponIconPictureBox).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion
        private Guna.UI2.WinForms.Guna2Panel guna2Panel2;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel1;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel3;

        private System.Windows.Forms.Label label2;
        private Guna.UI2.WinForms.Guna2Button guna2Button15;
        private Guna.UI2.WinForms.Guna2Button guna2Button2;
        private Guna.UI2.WinForms.Guna2Button guna2Button1;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel10;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel4;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel6;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel5;
        private Guna.UI2.WinForms.Guna2ProgressBar guna2ProgressBar3;
        private Guna.UI2.WinForms.Guna2ProgressBar guna2ProgressBar2;
        private Guna.UI2.WinForms.Guna2ProgressBar guna2ProgressBar1;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel7;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel9;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel8;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel12;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel11;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel13;
        private System.Windows.Forms.Label label5;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel14;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel15;
        private System.Windows.Forms.Label label6;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel16;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel17;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel18;
        private System.Windows.Forms.Label label7;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel19;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel20;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.PictureBox pictureBox6;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private Guna.UI2.WinForms.Guna2Button guna2Button10;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Timer timer1;
        private Guna.UI2.WinForms.Guna2ProgressBar guna2ProgressBar4;
        private System.Windows.Forms.Label label26;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel23;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox1;
        private System.Windows.Forms.Label label28;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel21;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel25;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label37;
        private Guna.UI2.WinForms.Guna2ComboBox guna2ComboBox1;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Label label43;
        private Guna.UI2.WinForms.Guna2Button guna2Button4;
        private Guna.UI2.WinForms.Guna2Button guna2Button3;
        private Guna.UI2.WinForms.Guna2Button guna2Button5;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox3;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox2;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox4;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox5;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox7;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox1;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox4;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox2;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox5;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox7;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox8;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox11;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox10;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox9;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox8;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox9;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox10;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox11;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox12;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel24;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel26;
        private System.Windows.Forms.Label label48;
        private System.Windows.Forms.Label label49;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox13;
        private System.Windows.Forms.Label label50;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox14;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox15;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox16;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox17;
        private Guna.UI2.WinForms.Guna2Button guna2Button6;
        private Guna.UI2.WinForms.Guna2Button guna2Button7;
        private Guna.UI2.WinForms.Guna2Button guna2Button8;
        private System.Windows.Forms.Label label51;
        private System.Windows.Forms.Label label52;
        private System.Windows.Forms.Label label53;
        private System.Windows.Forms.Label label54;
        private System.Windows.Forms.Label label55;
        private System.Windows.Forms.Label label56;
        private System.Windows.Forms.Label label57;
        private System.Windows.Forms.Label label58;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox19;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox18;
        private System.Windows.Forms.Label label59;
        private System.Windows.Forms.Label label60;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel30;
        private System.Windows.Forms.Label label61;
        private Guna.UI2.WinForms.Guna2Button guna2Button9;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel22;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel111;
        private System.Windows.Forms.Label label1;
        private Guna.UI2.WinForms.Guna2Button guna2Button11;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel29;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel28;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel27;
        private Guna.UI2.WinForms.Guna2Button guna2Button12;
        private Guna.UI2.WinForms.Guna2Button guna2Button18;
        private System.Windows.Forms.Timer timer2;
        private System.Windows.Forms.Timer timer3;
        private System.Windows.Forms.Timer timer4;
        private Guna.UI2.WinForms.Guna2PictureBox weaponIconPictureBox;
        private Label label62;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox20;
    }
}