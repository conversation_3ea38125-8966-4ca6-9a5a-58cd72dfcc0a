﻿using Guna.UI2.WinForms;
using Guna.UI2.WinForms.Suite;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using AotForms;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using AotForms;
using System.Threading;
using System.Text;
using System.Reflection.Emit;

namespace Client
{
    public partial class Form5 : Form
    {
        private const int ParticleCount = 100;
        private const int DrawCount = 90;
        private readonly Random _random = new Random();
        private readonly PointF[] _particlePositions = new PointF[ParticleCount];
        private readonly PointF[] _particleTargetPositions = new PointF[ParticleCount];
        private readonly float[] _particleSpeeds = new float[ParticleCount];
        private readonly float[] _particleSizes = new float[ParticleCount];
        private readonly float[] _particleRotations = new float[ParticleCount];

        private bool particlesEnabled = false; // Particelle disattivate all'avvio
                                               // Dichiara un array o una lista di colori per le particelle
        private Color[] _particleColors = new Color[ParticleCount]; // Usa il numero di particelle che hai
        private float selectedHue = 0; // Tono seleccionado
        private float currentSaturation = 0; // Saturación
        private float currentBrightness = 1; // Brillo
        private bool waitPressKey3 = false;
        //particels
        private int selectorX;
        private int brightnessSelectorX;
        private int brightnessSelectorY;
        private int brightnessSelectorX1;
        private int brightnessSelectorY1;
        private bool isDraggingColor = false;
        private bool isDraggingBrightness = false;
        private bool isDraggingBrightness2 = false;
        private Color selectedColor = Color.Red;
        private Bitmap colorGradientBitmap;
        private Bitmap originalPictureBoxImage;
        private Bitmap originalButtonImage;
        private Bitmap originalButtonImage1;

        private Bitmap originalButtonImage2;
        private Bitmap originalButtonImage3;
        private Bitmap originalPictureBoxImage1;
        private Bitmap originalPictureBoxImage2;
        private Bitmap originalPictureBoxImage3;
        private Bitmap originalPictureBoxImage5;
        private Bitmap originalPictureBoxImage4;
        private Bitmap originalPictureBoxImage6;
        private Bitmap originalPictureBoxImage7;

        private Guna2CircleButton selector;


        private Point selectorPosition = new Point(100, 100);
        //bbbb
        private Bitmap colorGradient22;
        private Bitmap brightnessGradient22;
        private Point colorSelectorPosition22;
        private Point brightnessSelectorPosition22;
        private bool isDraggingColor22 = false;
        private bool isDraggingBrightness22 = false;
        private float selectedHue22 = 0f;
        private Color selectedColor22;
        private List<Panel> savedColorPanels = new List<Panel>();
        private const int maxSavedColors = 4;
        private int savedColorOffsetY22 = 12;
        private System.Windows.Forms.Timer colorUpdateTimer;

        private Color targetColor; // Colore target per la transizione
        //zbbbb
        private Random random = new Random();
        private Color[] warmColors = new Color[]
        {
            Color.FromArgb(255, 99, 71),   // Tomato
            Color.FromArgb(255, 127, 80),  // Coral
            Color.FromArgb(255, 215, 0),   // Gold
            Color.FromArgb(255, 165, 0),   // Orange
            Color.FromArgb(255, 69, 0)     // OrangeRed
        };
        //private int startX;
        // Singleton instance

        // Rendi la label pubblica o aggiungi una proprietà pubblica

        // Rendi la label accessibile
        public System.Windows.Forms.Label WeaponLogLabel { get; private set; }
        // Singleton pattern
        private static Form5 _instance;
        public static Form5 Instance => _instance ??= new Form5();
        public Form5(/*IntPtr handle*/)
        {
            //mainHandle = handle;
            InitializeComponent();
            GenerateBrightnessGradient22();
            GenerateColorGradient22();
            colorSelectorPosition22 = new Point(0, guna2Panel28.Height - 1);
            brightnessSelectorPosition22 = new Point(0, guna2Panel29.Height / 2);

            // Configura il Timer
            colorUpdateTimer = new System.Windows.Forms.Timer();
            colorUpdateTimer.Interval = 30; // Impostiamo l'intervallo di 30ms per un aggiornamento più fluido
            colorUpdateTimer.Tick += (s, e) => { UpdateColorSmoothly(); };

            // Eventi per i selettori
            guna2Panel28.MouseDown += (s, e) => { isDraggingColor = true; MoveColorSelector(e.Location); };
            guna2Panel28.MouseMove += (s, e) => { if (isDraggingColor) MoveColorSelector(e.Location); };
            guna2Panel28.MouseUp += (s, e) => { isDraggingColor = false; };

            guna2Panel29.MouseDown += (s, e) => { isDraggingBrightness = true; MoveBrightnessSelector(e.Location); };
            guna2Panel29.MouseMove += (s, e) => { if (isDraggingBrightness) MoveBrightnessSelector(e.Location); };
            guna2Panel29.MouseUp += (s, e) => { isDraggingBrightness = false; };


            if (guna2Button11.Image != null)
            {
                originalButtonImage22 = new Bitmap(guna2Button11.Image);
            }
            guna2Button11.Click += SaveSelectedColor;

            if (guna2PictureBox8.Image != null)
            {
                originalButtonImage55 = new Bitmap(guna2PictureBox8.Image);
            }
            if (guna2PictureBox9.Image != null)
            {
                originalButtonImage44 = new Bitmap(guna2PictureBox9.Image);
            }
            if (guna2PictureBox10.Image != null)
            {
                originalButtonImage10 = new Bitmap(guna2PictureBox10.Image);
            }
            if (guna2PictureBox11.Image != null)
            {
                originalButtonImage11 = new Bitmap(guna2PictureBox11.Image);
            }
            //hhhhh n3as n3as hhhh

            //zbbb
            hookCallback3 = new LowLevelKeyboardProc(HookCallback3);
            hookID3 = SetHook3(hookCallback3);
            //particelszbb
            this.FormBorderStyle = FormBorderStyle.None; // Rimuove il bordo
            this.StartPosition = FormStartPosition.CenterScreen; // Posiziona al cent
            _instance = this;





            CreaGradiente();
            guna2Panel3.Paint += Guna2Panel3_Paint;
            guna2Panel3.MouseDown += Guna2Panel3_MouseDown;
            guna2Panel3.MouseMove += Guna2Panel3_MouseMove;
            guna2Panel3.MouseUp += Guna2Panel3_MouseUp;
            guna2Panel1.Paint += Guna2Panel1_Paint;
            guna2Panel1.MouseDown += Guna2Panel1_MouseDown;
            guna2Panel1.MouseMove += Guna2Panel1_MouseMove;
            guna2Panel1.MouseUp += Guna2Panel1_MouseUp;



            CreaGraddiente();
            CreaGraddientePanel30();
            CreaGraddientePanel24();
            guna2Panel22.MouseUp += Guna2Panel22_MouseUp;
            guna2Panel22.MouseDown += Guna2Panel22_MouseDown;
            guna2Panel22.MouseMove += Guna2Panel22_MouseMove;

            guna2Panel30.MouseUp += Guna2Panel30_MouseUp;
            guna2Panel30.MouseDown += Guna2Panel30_MouseDown;
            guna2Panel30.MouseMove += Guna2Panel30_MouseMove;

            guna2Panel24.MouseUp += Guna2Panel24_MouseUp;
            guna2Panel24.MouseDown += Guna2Panel24_MouseDown;
            guna2Panel24.MouseMove += Guna2Panel24_MouseMove;
            // particles zbbb 
            guna2CustomCheckBox19.CheckedChanged += guna2CustomCheckBox19_CheckedChanged;
            //bbb
            guna2Panel22.Paint += Guna2Panel22_Paint;

            hookCallback4 = new LowLevelKeyboardProc(HookCallback4);
            hookID4 = SetHook4(hookCallback4);

            if (guna2Panel11 != null)
            {
                guna2Panel11.Click += guna2Panel11_Click;

            }


            if (guna2Panel19 != null)
            {
                guna2Panel19.Click += guna2Panel19_Click;

            }
            if (guna2Panel17 != null)
            {
                guna2Panel17.Click += guna2Panel17_Click;
            }
            // Salva l'immagine originale di guna2Button15
            if (guna2Button15.Image != null)
            {
                originalButtonImage = new Bitmap(guna2Button15.Image);

            }
            if (guna2PictureBox2.Image != null)
            {
                originalPictureBoxImage2 = new Bitmap(guna2PictureBox2.Image);

            }
            if (guna2PictureBox6.Image != null)
            {
                originalPictureBoxImage6 = new Bitmap(guna2PictureBox6.Image);

            }
            if (guna2PictureBox7.Image != null)
            {
                originalPictureBoxImage7 = new Bitmap(guna2PictureBox7.Image);

            }
            if (guna2PictureBox4.Image != null)
            {
                originalPictureBoxImage4 = new Bitmap(guna2PictureBox4.Image);

            }

            if (guna2PictureBox5.Image != null)
            {
                originalPictureBoxImage5 = new Bitmap(guna2PictureBox5.Image);

            }

            if (guna2PictureBox3.Image != null)
            {
                originalPictureBoxImage3 = new Bitmap(guna2PictureBox3.Image);

            }
            if (guna2PictureBox1.Image != null)
            {
                originalButtonImage2 = new Bitmap(guna2PictureBox1.Image);

            }
            if (guna2Button1.Image != null)
            {
                originalButtonImage1 = new Bitmap(guna2Button1.Image);
            }

            if (guna2Button2.Image != null)
            {
                originalPictureBoxImage = new Bitmap(guna2Button2.Image);
            }
            // particles zbbb 
            InitializeParticles();
            DoubleBuffered = true;
            System.Windows.Forms.Timer particleTimer = new System.Windows.Forms.Timer
            {
                Interval = 3 // ~60 FPS
            };
            particleTimer.Tick += (sender, args) =>
            {
                if (particlesEnabled)
                {
                    UpdateParticles();
                    Invalidate(); // Aggiorna la finestra
                    guna2Panel1.Invalidate();
                    guna2Panel2.Invalidate();
                    guna2Panel3.Invalidate();
                    guna2Panel4.Invalidate();
                    guna2Panel5.Invalidate();
                    guna2Panel6.Invalidate();
                    guna2Panel7.Invalidate();
                    guna2Panel8.Invalidate();
                    guna2Panel9.Invalidate();
                    guna2Panel10.Invalidate();
                    guna2Panel11.Invalidate();
                    guna2Panel12.Invalidate();
                    guna2Panel14.Invalidate();
                    guna2Panel17.Invalidate();
                    guna2Panel16.Invalidate();
                    guna2Panel19.Invalidate();
                }
            };
            particleTimer.Start();
        }


        private void GenerateColorGradient22()
        {
            colorGradient22 = new Bitmap(guna2Panel28.Width, guna2Panel28.Height);
            for (int x = 0; x < colorGradient22.Width; x++)
            {
                for (int y = 0; y < colorGradient22.Height; y++)
                {
                    float brightness = 1 - (float)y / colorGradient22.Height;
                    colorGradient22.SetPixel(x, y, ColorFromHSV(selectedHue22, (float)x / colorGradient22.Width, brightness));
                }
            }
            guna2Panel28.Invalidate();
        }

        private void GenerateBrightnessGradient22()
        {
            brightnessGradient22 = new Bitmap(guna2Panel29.Width, guna2Panel29.Height);
            for (int x = 0; x < brightnessGradient22.Width; x++)
            {
                float hue = (float)x / brightnessGradient22.Width * 360;
                for (int y = 0; y < brightnessGradient22.Height; y++)
                {
                    brightnessGradient22.SetPixel(x, y, ColorFromHSV(hue, 1, 1));
                }
            }
        }

        private void DrawSelector22(Graphics g, Point position)
        {
            g.DrawEllipse(Pens.Black, position.X - 3, position.Y - 3, 6, 6);
            g.DrawEllipse(Pens.White, position.X - 4, position.Y - 4, 8, 8);
        }
        // Aggiungi questo metodo per aggiornare i log
        // Metodo per aggiornare i log
        public void UpdateWeaponLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateWeaponLog(message)));
                return;
            }

            WeaponLogLabel.Text = $"[{DateTime.Now:HH:mm:ss}] {message}\n{WeaponLogLabel.Text}";

            // Limita a 20 righe
            var lines = WeaponLogLabel.Text.Split('\n');
            if (lines.Length > 20)
            {
                WeaponLogLabel.Text = string.Join("\n", lines.Take(20));
            }
        }
        private Color ColorFromHSV(float hue, float saturation, float brightness)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            float f = hue / 60 - (float)Math.Floor(hue / 60);
            brightness *= 255;
            int v = (int)brightness;
            int p = (int)(brightness * (1 - saturation));
            int q = (int)(brightness * (1 - f * saturation));
            int t = (int)(brightness * (1 - (1 - f) * saturation));

            if (hi == 0) return Color.FromArgb(v, t, p);
            if (hi == 1) return Color.FromArgb(q, v, p);
            if (hi == 2) return Color.FromArgb(p, v, t);
            if (hi == 3) return Color.FromArgb(p, q, v);
            if (hi == 4) return Color.FromArgb(t, p, v);
            return Color.FromArgb(v, p, q);
        }

        private void MoveColorSelector(Point location)
        {
            colorSelectorPosition22 = new Point(Math.Max(0, Math.Min(location.X, guna2Panel28.Width - 1)),
                                              Math.Max(0, Math.Min(location.Y, guna2Panel28.Height - 1)));

            // Ottieni il colore selezionato e salvalo come target per l'animazione
            targetColor = colorGradient22.GetPixel(colorSelectorPosition22.X, colorSelectorPosition22.Y);
            colorUpdateTimer.Start(); // Avvia il timer per un cambiamento graduale
        }

        private void MoveBrightnessSelector(Point location)
        {
            // Aggiorna la posizione del selettore
            brightnessSelectorPosition22 = new Point(
                Math.Max(0, Math.Min(location.X, guna2Panel29.Width - 1)),
                guna2Panel29.Height / 2 // Mantieni il selettore al centro in altezza
            );

            // Calcola il nuovo Hue (tonalità) in base alla posizione del selettore
            selectedHue22 = (float)brightnessSelectorPosition22.X / guna2Panel29.Width * 360;

            // Rigenera il gradiente di colori nel guna2Panel28 con il nuovo Hue
            GenerateColorGradient22();
            guna2Panel27.ShadowDecoration.Color = selectedColor22;
            // Aggiorna il colore selezionato e il suo stato visivo
            targetColor = ColorFromHSV(selectedHue22, 1, 1);
            colorUpdateTimer.Start(); // Avvia l'animazione del colore

            // Forza il ridisegno dei pannelli per aggiornare il selettore e il gradiente
            guna2Panel29.Invalidate();
            guna2Panel28.Invalidate();
        }


        private void UpdateColorSmoothly()
        {
            if (selectedColor22.Equals(targetColor))
            {
                colorUpdateTimer.Stop();
                return;
            }

            int r = (int)(selectedColor22.R + (targetColor.R - selectedColor22.R) * 0.1f);
            int g = (int)(selectedColor22.G + (targetColor.G - selectedColor22.G) * 0.1f);
            int b = (int)(selectedColor22.B + (targetColor.B - selectedColor22.B) * 0.1f);
            selectedColor22 = Color.FromArgb(r, g, b);

            // Aggiorna i colori specifici per ESPLine o ESPBox
            if (currentTarget == TargetImage.PictureBox8)
            {
                Config.ESPLineColor = selectedColor22;
            }
            else if (currentTarget == TargetImage.PictureBox9)
            {
                Config.ESPBoxColor = selectedColor22;
            }
            else if (currentTarget == TargetImage.PictureBox11)
            {
                Config.ESPSkeletonColor = selectedColor22;
            }
            else if (currentTarget == TargetImage.PictureBox10)
            {
                Config.ESPNameColor = selectedColor22;
            }
            guna2Panel27.ShadowDecoration.Color = selectedColor22;
            guna2Panel29.ShadowDecoration.Color = selectedColor22;
            guna2Panel28.ShadowDecoration.Color = selectedColor22;
            guna2Panel111.ShadowDecoration.Color = selectedColor22;

            label1.Text = $"Color: {selectedColor22.Name.ToUpper()}";
            label1.ForeColor = selectedColor22;

            UpdateButtonImageColor22();
            if (currentTarget == TargetImage.PictureBox11)
            {
                UpdateButtonImageColor11();
            }
            if (currentTarget == TargetImage.PictureBox10)
            {
                UpdateButtonImageColor10();
            }
            if (currentTarget == TargetImage.PictureBox8)
            {
                UpdateButtonImageColor55();
            }
            else if (currentTarget == TargetImage.PictureBox9)
            {
                UpdateButtonImageColor44();
            }

            guna2Panel28.Invalidate();
        }


        private void SaveSelectedColor(object sender, EventArgs e)
        {
            if (savedColorPanels.Count >= maxSavedColors) return;

            int offsetY = 12 + (savedColorPanels.Count * 40); // Ogni cerchio 40px sotto il precedente

            Panel newColorPanel = new Panel()
            {
                Size = new Size(23, 24),
                Location = new Point(226, offsetY), // Posizionamento verticale
                BackColor = selectedColor22,
                BorderStyle = BorderStyle.FixedSingle,
                Parent = guna2Panel27
            };

            newColorPanel.Click += (s, ev) =>
            {
                Panel clickedPanel = s as Panel; // Converte il sender in un pannello
                if (clickedPanel != null)
                {
                    colorUpdateTimer.Stop(); // Ferma il timer per evitare che il colore venga cambiato automaticamente
                    selectedColor22 = clickedPanel.BackColor; // Aggiorna il colore selezionato
                    label1.ForeColor = selectedColor22; // Cambia il ForeColor di label1
                }
            };


            Button closeButton = new Button()
            {
                Size = new Size(15, 15),
                Location = new Point(15, -5),
                Text = "X",
                Font = new Font("Arial", 6, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
            };

            closeButton.Click += (s, ev) =>
            {
                guna2Panel27.Controls.Remove(newColorPanel);
                savedColorPanels.Remove(newColorPanel);
                ReorderSavedColors(); // Sposta i rimanenti verso l'alto
            };

            newColorPanel.Controls.Add(closeButton);
            savedColorPanels.Add(newColorPanel);
        }


        // Metodo per riordinare i cerchi quando uno viene eliminato
        private void ReorderSavedColors()
        {
            for (int i = 0; i < savedColorPanels.Count; i++)
            {
                savedColorPanels[i].Location = new Point(226, 12 + (i * 40)); // Riordina verticalmente
            }
        }



        private Bitmap originalButtonImage22;

        private void UpdateButtonImageColor22()
        {
            if (originalButtonImage22 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage22.Width, originalButtonImage22.Height);

                for (int x = 0; x < originalButtonImage22.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage22.Height; y++)
                    {
                        Color pixelColor = originalButtonImage22.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor22);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2Button11.Image = newImage;
            }
        }
        private Bitmap originalButtonImage55;

        private void UpdateButtonImageColor55()
        {
            if (originalButtonImage55 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage55.Width, originalButtonImage55.Height);

                for (int x = 0; x < originalButtonImage55.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage55.Height; y++)
                    {
                        Color pixelColor = originalButtonImage55.GetPixel(x, y);

                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor22);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox8.Image = newImage;


            }
        }

        private Bitmap originalButtonImage44;

        private void UpdateButtonImageColor44()
        {
            if (originalButtonImage44 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage44.Width, originalButtonImage44.Height);

                for (int x = 0; x < originalButtonImage44.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage44.Height; y++)
                    {
                        Color pixelColor = originalButtonImage44.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor22);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox9.Image = newImage;
            }
        }
        private Bitmap originalButtonImage10;

        private void UpdateButtonImageColor10()
        {
            if (originalButtonImage10 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage10.Width, originalButtonImage10.Height);

                for (int x = 0; x < originalButtonImage10.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage10.Height; y++)
                    {
                        Color pixelColor = originalButtonImage10.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor22);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox10.Image = newImage;
            }
        }
        private Bitmap originalButtonImage11;

        private void UpdateButtonImageColor11()
        {
            if (originalButtonImage11 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage11.Width, originalButtonImage11.Height);

                for (int x = 0; x < originalButtonImage11.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage11.Height; y++)
                    {
                        Color pixelColor = originalButtonImage11.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor22);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox11.Image = newImage;
            }
        }
        private enum TargetImage
        {
            None,
            PictureBox8,
            PictureBox9,
            PictureBox10,
            PictureBox11
        }

        private TargetImage currentTarget = TargetImage.None;

        // Eventos de mouse en el panel de tonos (ColorBase)
        private Color _particleColor = Color.White; // Colore iniziale delle particelle
        private void InitializeParticles()
        {
            // Inizializza le particelle fuori dallo schermo
            for (int i = 0; i < ParticleCount; i++)
            {
                _particlePositions[i] = new PointF(-100, -100); // Fuori dal viewport
                _particleTargetPositions[i] = new PointF(-100, -100);
                _particleSpeeds[i] = 0;
                _particleSizes[i] = 0;
                _particleRotations[i] = 0;
            }
        }

        private void UpdateParticles()
        {
            Size screenSize = Screen.PrimaryScreen.Bounds.Size;
            for (int i = 0; i < ParticleCount; i++)
            {
                if (_particlePositions[i].X == -100 && _particlePositions[i].Y == -100)
                {
                    _particlePositions[i] = new PointF(_random.Next(screenSize.Width + 1), 15f);
                    _particleSpeeds[i] = 1 + _random.Next(25);
                    _particleSizes[i] = _random.Next(8);
                    _particleTargetPositions[i] = new PointF(_random.Next(screenSize.Width), screenSize.Height * 2);
                }

                float deltaTime = 1.0f / 60; // ~60 FPS
                _particlePositions[i] = Lerp(_particlePositions[i], _particleTargetPositions[i], deltaTime * (_particleSpeeds[i] / 60));
                _particleRotations[i] += deltaTime;

                if (_particlePositions[i].Y > screenSize.Height)
                {
                    _particlePositions[i] = new PointF(_random.Next(screenSize.Width + 1), 15f);
                    _particleTargetPositions[i] = new PointF(_random.Next(screenSize.Width), screenSize.Height * 2);
                    _particleRotations[i] = 0;
                }
            }
        }

        private PointF Lerp(PointF start, PointF end, float t)
        {
            return new PointF(start.X + (end.X - start.X) * t, start.Y + (end.Y - start.Y) * t);
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // Crea un oggetto Graphics per disegnare l'ombra
            Graphics g = e.Graphics;

            // Crea il pennello per disegnare l'ombra con il colore selezionato
            using (Brush shadowBrush = new SolidBrush(selectedColor))
            {
                // Disegna l'ombra leggermente spostata
                g.FillRectangle(shadowBrush, 10, 10, this.Width - 20, this.Height - 20);
            }

            // Disegna il form principale sopra l'ombra
            using (Brush backgroundBrush = new SolidBrush(this.BackColor))
            {
                g.FillRectangle(backgroundBrush, 0, 0, this.Width, this.Height);
            }
        }








        private void Form_Paint(object sender, PaintEventArgs e)
        {
            // Ora puoi usare 'e' per accedere a 'e.Graphics'
            DrawParticleEffect(e.Graphics);
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        private static IntPtr hookID = IntPtr.Zero;
        private static IntPtr hookID2 = IntPtr.Zero;
        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);



        private void guna2CustomCheckBox19_CheckedChanged(object sender, EventArgs e)
        {
            particlesEnabled = guna2CustomCheckBox19.Checked;

            if (!particlesEnabled)
            {
                // Disattiva le particelle immediatamente
                for (int i = 0; i < ParticleCount; i++)
                {
                    _particlePositions[i] = new PointF(-100, -100); // Fuori dallo schermo
                }

                // Aggiorna i pannelli per rimuovere le particelle visibili
                // Invalida tutti i pannelli dalla 1 alla 10
                guna2Panel1.Invalidate();
                guna2Panel2.Invalidate();
                guna2Panel3.Invalidate();
                guna2Panel4.Invalidate();
                guna2Panel5.Invalidate();
                guna2Panel6.Invalidate();
                guna2Panel7.Invalidate();
                guna2Panel8.Invalidate();
                guna2Panel9.Invalidate();
                guna2Panel10.Invalidate();
                guna2Panel11.Invalidate();
                guna2Panel12.Invalidate();
                guna2Panel14.Invalidate();
                guna2Panel17.Invalidate();
                guna2Panel16.Invalidate();
                guna2Panel19.Invalidate();
            }
        }

        private void PaintParticlesOnPanel(PaintEventArgs e, Panel panel)
        {
            e.Graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;

            for (int i = 0; i < DrawCount; i++)
            {
                if (IsParticleOverPanel(_particlePositions[i], panel))
                {
                    DrawTriangleWithGlow(e.Graphics, panel.PointToClient(Point.Round(_particlePositions[i])), _particleSizes[i], _particleRotations[i]);
                }
            }
        }

        private bool IsParticleOverPanel(PointF particle, Panel panel)
        {
            var screenPosition = panel.PointToScreen(Point.Empty);
            var panelRectangle = new RectangleF(screenPosition.X, screenPosition.Y, panel.Width, panel.Height);

            return panelRectangle.Contains(particle);
        }

        private void DrawParticleEffect(Graphics graphics)
        {
            // Esegui il disegno per ogni particella
            for (int i = 0; i < ParticleCount; i++)
            {
                PointF particlePosition = _particlePositions[i];
                float size = _particleSizes[i];
                float rotation = _particleRotations[i];

                // Disegna il triangolo con il bagliore utilizzando il colore aggiornato
                DrawTriangleWithGlow(graphics, particlePosition, size, rotation);
            }
        }

        private void DrawTriangleWithGlow(Graphics graphics, PointF position, float size, float rotation)
        {
            // Calcola i vertici del triangolo
            PointF[] vertices = new PointF[3];
            float angle = (float)(Math.PI * 2 / 3); // 120 gradi per un triangolo equilatero

            for (int i = 0; i < 3; i++)
            {
                vertices[i] = new PointF(
                    position.X + size * (float)Math.Cos(rotation + i * angle),
                    position.Y + size * (float)Math.Sin(rotation + i * angle)
                );
            }

            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

            // Disegna l'effetto di bagliore
            int maxGlowLayers = 10;
            for (int j = 0; j < maxGlowLayers; j++)
            {
                int alpha = 25 - 2 * j; // Decrementa l'alpha per il bagliore
                using (Brush glowBrush = new SolidBrush(Color.FromArgb(alpha, _particleColor.R, _particleColor.G, _particleColor.B)))
                {
                    float glowSize = size + j * 2;
                    graphics.FillEllipse(glowBrush, position.X - glowSize / 2, position.Y - glowSize / 2, glowSize, glowSize);
                }
            }

            // Disegna il triangolo con il colore delle particelle
            using (Brush brush = new SolidBrush(_particleColor))
            {
                graphics.FillPolygon(brush, vertices);
            }
        }

        // particles zbbb 








        // particles zbbb 


        //private Color ColorFromHSV(double hue, double saturation, double value)
        //{
        //    int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
        //    double f = hue / 60 - Math.Floor(hue / 60);
        //    int v = Convert.ToInt32(value * 255);
        //    int p = Convert.ToInt32(v * (1 - saturation));
        //    int q = Convert.ToInt32(v * (1 - f * saturation));
        //    int t = Convert.ToInt32(v * (1 - (1 - f) * saturation));

        //    switch (hi)
        //    {
        //        case 0: return Color.FromArgb(255, v, t, p);
        //        case 1: return Color.FromArgb(255, q, v, p);
        //        case 2: return Color.FromArgb(255, p, v, t);
        //        case 3: return Color.FromArgb(255, p, q, v);
        //        case 4: return Color.FromArgb(255, t, p, v);
        //        default: return Color.FromArgb(255, v, p, q);
        //    }
        //}




        private void StopDragging(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }






        private void CreaGraddiente()
        {
            Bitmap bmp = new Bitmap(guna2Panel22.Width, guna2Panel22.Height);
            using (Graphics g = Graphics.FromImage(bmp))
            {
                Rectangle rect = new Rectangle(0, 0, bmp.Width, bmp.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect,
                        Color.Red, Color.Violet, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] { Color.Red, Color.Orange, Color.Yellow, Color.Green, Color.Cyan, Color.Blue, Color.Violet },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel22.BackgroundImage = bmp;
            selectorX = guna2Panel22.Width - 20;
            guna2Panel22.Invalidate();
        }

        private void Guna2Panel22_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.X >= 0 && e.X < guna2Panel22.Width)
            {
                selectorX = e.X;
                guna2Panel22.Invalidate();
                UpdateColor();
            }
        }

        private void Guna2Panel22_MouseDown(object sender, MouseEventArgs e)
        {
            isDragging = true;
        }

        private void Guna2Panel22_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        private void Guna2Panel22_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics);
        }
        private bool isDragging = false;
        private void DrawSelector(Graphics g)
        {
            int circleY = guna2Panel22.Height / 2 - 3;
            int circleSize = 6;

            using (Pen borderPen = new Pen(Color.Black, 1))
            using (SolidBrush brush = new SolidBrush(Color.White))
            {
                g.FillEllipse(brush, selectorX - circleSize / 2, circleY, circleSize, circleSize);
                g.DrawEllipse(borderPen, selectorX - circleSize / 2, circleY, circleSize, circleSize);
            }
        }

        private void UpdateColor()
        {
            if (guna2Panel22.BackgroundImage is Bitmap bmp)
            {
                selectedColor = bmp.GetPixel(selectorX, guna2Panel22.Height / 2);
                label40.ForeColor = selectedColor;
                label40.Text = $"Color: {selectedColor.R}, {selectedColor.G}, {selectedColor.B}";





                guna2Panel22.ShadowDecoration.Color = selectedColor;

            }
        }



        private void CreaGraddientePanel30()
        {
            Bitmap bmp = new Bitmap(guna2Panel30.Width, guna2Panel30.Height);
            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.FromArgb(15, 15, 14)); // Inizialmente tutto di colore 15,15,14
            }
            guna2Panel30.BackgroundImage = bmp;
            selectorX = 0; // Inizialmente il cerchio è a sinistra
            guna2Panel30.Invalidate();
        }

        private void Guna2Panel30_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingPanel30 && e.X >= 0 && e.X < guna2Panel30.Width)
            {
                selectorX = e.X;
                guna2Panel30.Invalidate();
                UpdateColorPanel30();
                // Calcola la percentuale della posizione corrente del selettore
                double percentage = (double)selectorX / guna2Panel30.Width;

                // Scala la percentuale nell'intervallo da 1 a 50
                int value = (int)Math.Round(percentage * 49) + 1;

                // Aggiorna il testo di label59
                label60.Text = value.ToString();
            }
        }

        private void Guna2Panel30_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingPanel30 = true;
        }

        private void Guna2Panel30_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingPanel30 = false;
        }

        private void Guna2Panel30_Paint(object sender, PaintEventArgs e)
        {
            DrawSelectorPanel30(e.Graphics);
        }

        private bool isDraggingPanel30 = false;
        private void DrawSelectorPanel30(Graphics g)
        {
            int circleY = guna2Panel30.Height / 2 - 3;
            int circleSize = 6;

            using (Pen borderPen = new Pen(Color.Black, 1))
            using (SolidBrush brush = new SolidBrush(Color.White))
            {
                g.FillEllipse(brush, selectorX - circleSize / 2, circleY, circleSize, circleSize);
                g.DrawEllipse(borderPen, selectorX - circleSize / 2, circleY, circleSize, circleSize);
            }
        }

        private void UpdateColorPanel30()
        {


            // Usa un colore predefinito o un colore già esistente
            Color currentColor = label2.ForeColor; // O qualsiasi altro colore di default
            UpdateColorPanel30(currentColor);

        }

        private void UpdateColorPanel30(Color newColor)
        {
            // Verifica che guna2Panel30 non sia null e che il suo BackgroundImage sia un Bitmap
            if (guna2Panel30?.BackgroundImage is Bitmap bmp)
            {
                for (int x = 0; x < bmp.Width; x++)
                {
                    for (int y = 0; y < bmp.Height; y++)
                    {
                        if (x <= selectorX)
                        {
                            bmp.SetPixel(x, y, newColor);
                        }
                        else
                        {
                            bmp.SetPixel(x, y, Color.FromArgb(15, 15, 14));
                        }
                    }
                }
                guna2Panel30.BackgroundImage = bmp;
            }
            else
            {
                throw new InvalidOperationException("BackgroundImage di guna2Panel30 non è un'istanza di Bitmap.");
            }
        }


        private void CreaGraddientePanel24()
        {
            Bitmap bmp = new Bitmap(guna2Panel24.Width, guna2Panel24.Height);
            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.FromArgb(15, 15, 14)); // Inizialmente tutto di colore 15,15,14
            }
            guna2Panel24.BackgroundImage = bmp;
            selectorX = 0; // Inizialmente il cerchio è a sinistra
            guna2Panel24.Invalidate();
        }

        private void Guna2Panel24_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingPanel24 && e.X >= 0 && e.X < guna2Panel24.Width)
            {
                selectorX = e.X;
                guna2Panel24.Invalidate();
                UpdateColorPanel24();
                // Calcola la percentuale della posizione corrente del selettore
                double percentage = (double)selectorX / guna2Panel24.Width;

                // Scala la percentuale nell'intervallo da 1 a 50
                int value = (int)Math.Round(percentage * 49) + 1;

                // Aggiorna il testo di label59
                label59.Text = value.ToString();
            }
        }

        private void Guna2Panel24_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingPanel24 = true;
        }

        private void Guna2Panel24_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingPanel24 = false;
        }

        private void Guna2Panel24_Paint(object sender, PaintEventArgs e)
        {
            DrawSelectorPanel24(e.Graphics);
        }

        private bool isDraggingPanel24 = false;
        private void DrawSelectorPanel24(Graphics g)
        {
            int circleY = guna2Panel24.Height / 2 - 3;
            int circleSize = 6;

            using (Pen borderPen = new Pen(Color.Black, 1))
            using (SolidBrush brush = new SolidBrush(Color.White))
            {
                g.FillEllipse(brush, selectorX - circleSize / 2, circleY, circleSize, circleSize);
                g.DrawEllipse(borderPen, selectorX - circleSize / 2, circleY, circleSize, circleSize);
            }
        }

        private void UpdateColorPanel24()
        {
            if (guna2Panel24.BackgroundImage is Bitmap bmp)
            {
                Color newColor = label2.ForeColor; // Prende il colore aggiornato da UpdateLabelColor()

                for (int x = 0; x < bmp.Width; x++)
                {
                    for (int y = 0; y < bmp.Height; y++)
                    {
                        if (x <= selectorX)
                        {
                            bmp.SetPixel(x, y, newColor); // Sostituisce il blu con il colore aggiornato
                        }
                        else
                        {
                            bmp.SetPixel(x, y, Color.FromArgb(15, 15, 14)); // Mantiene il colore originale
                        }
                    }
                }
                guna2Panel24.BackgroundImage = bmp;
            }
        }


        private void guna2Panel11_Click(object sender, EventArgs e)
        {
            guna2Panel27.Visible = false;
            guna2Panel28.Visible = false;
            guna2Panel29.Visible = false;

        }
        private void guna2Panel19_Click(object sender, EventArgs e)
        {
            guna2Panel1.Visible = false;
            guna2Panel2.Visible = false;
            guna2Panel3.Visible = false;
            guna2Panel23.Visible = false;

        }
        private void guna2Panel17_Click(object sender, EventArgs e)
        {
            guna2Panel1.Visible = false;
            guna2Panel2.Visible = false;
            guna2Panel3.Visible = false;
            guna2Panel23.Visible = false;

        }
        private void CreaGradiente()
        {
            colorGradientBitmap = new Bitmap(guna2Panel3.Width, guna2Panel3.Height);
            using (Graphics g = Graphics.FromImage(colorGradientBitmap))
            {
                Rectangle rect = new Rectangle(0, 0, colorGradientBitmap.Width, colorGradientBitmap.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.Red, Color.Violet, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] { Color.Red, Color.Orange, Color.Yellow, Color.Green, Color.Cyan, Color.Blue, Color.Violet },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel3.BackgroundImage = colorGradientBitmap;
            selectorX = guna2Panel3.Width - 20;
            guna2Panel3.Invalidate();
        }

        private void Guna2Panel3_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics, selectorX, guna2Panel3.Height / 2);
        }

        private void Guna2Panel3_MouseDown(object sender, MouseEventArgs e)
        {




            isDraggingColor = true;
            UpdateColor(e.X);

        }

        private void Guna2Panel3_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingColor && e.X >= 0 && e.X < guna2Panel3.Width)
            {
                UpdateColor(e.X);
            }
        }

        private void Guna2Panel3_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingColor = false;
        }

        private void DrawSelector(Graphics g, int x, int y)
        {
            int circleSize = 10;
            using (Pen borderPen = new Pen(Color.Black, 1))
            using (SolidBrush brush = new SolidBrush(Color.White))
            {
                g.FillEllipse(brush, x - circleSize / 2, y - circleSize / 2, circleSize, circleSize);
                g.DrawEllipse(borderPen, x - circleSize / 2, y - circleSize / 2, circleSize, circleSize);
            }
        }

        private void UpdateColor(int x)
        {
            selectorX = x;
            if (colorGradientBitmap != null)
            {
                selectorX = Math.Max(0, Math.Min(selectorX, colorGradientBitmap.Width - 1));
                selectedColor = colorGradientBitmap.GetPixel(selectorX, guna2Panel3.Height / 2);
                _particleColor = selectedColor; // Aggiorna il colore delle particelle
                guna2Panel1.Invalidate();
                UpdateLabelColor();  // Aggiorna subito il ForeColor di label2
            }
            guna2Panel3.Invalidate();
        }

        private void Guna2Panel1_Paint(object sender, PaintEventArgs e)
        {
            using (LinearGradientBrush brush = new LinearGradientBrush(guna2Panel1.ClientRectangle, Color.White, selectedColor, LinearGradientMode.ForwardDiagonal))
            {
                ColorBlend blend = new ColorBlend();
                blend.Colors = new Color[] { Color.White, selectedColor, Color.FromArgb(10, 10, 10), Color.FromArgb(10, 10, 10) };
                blend.Positions = new float[] { 0.0f, 0.33f, 0.66f, 1.0f };
                brush.InterpolationColors = blend;
                e.Graphics.FillRectangle(brush, guna2Panel1.ClientRectangle);
            }
            DrawSelector(e.Graphics, brightnessSelectorX, brightnessSelectorY);
        }


        private void Guna2Panel1_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingBrightness = true;
            UpdateBrightness(e.X, e.Y);
        }

        private void Guna2Panel1_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingBrightness)
            {
                UpdateBrightness(e.X, e.Y);
            }
        }

        private void Guna2Panel1_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingBrightness = false;
        }

        private void UpdateBrightness(int x, int y)
        {
            brightnessSelectorX = Math.Max(0, Math.Min(x, guna2Panel1.Width - 1));
            brightnessSelectorY = Math.Max(0, Math.Min(y, guna2Panel1.Height - 1));

            UpdateLabelColor(); // Aggiorna ForeColor con la luminosità scelta
            guna2Panel1.Invalidate();
        }

        private void UpdateLabelColor()
        {
            float brightnessFactor = 1 - (float)brightnessSelectorY / guna2Panel1.Height;
            int red = (int)(selectedColor.R * brightnessFactor);
            int green = (int)(selectedColor.G * brightnessFactor);
            int blue = (int)(selectedColor.B * brightnessFactor);

            Color adjustedColor = Color.FromArgb(red, green, blue);

            // Aggiorna il colore dei Label da 2 a 8
            for (int i = 2; i <= 8; i++)
            {
                var label = this.Controls.Find($"label{i}", true).FirstOrDefault() as System.Windows.Forms.Label;
                if (label != null)
                {
                    label.ForeColor = adjustedColor;
                }
            }



            // ... codice esistente per aggiornare altri controlli ...

            // Aggiorna il colore di guna2Panel30
            UpdateColorPanel30(adjustedColor);
            // Aggiorna il colore dei guna2CustomCheckBox da 1 a 19
            for (int i = 1; i <= 19; i++)
            {
                var checkBox = this.Controls.Find($"guna2CustomCheckBox{i}", true).FirstOrDefault() as Guna.UI2.WinForms.Guna2CustomCheckBox;
                if (checkBox != null)
                {
                    checkBox.CheckedState.FillColor = adjustedColor;
                    checkBox.ShadowDecoration.Color = adjustedColor;
                }
            }

            // Aggiorna il colore degli altri controlli specificati
            label28.ForeColor = adjustedColor;
            pictureBox6.BackColor = adjustedColor;
            guna2Panel1.ShadowDecoration.Color = adjustedColor;
            guna2Panel2.ShadowDecoration.Color = adjustedColor;
            guna2Panel3.ShadowDecoration.Color = adjustedColor;
            guna2Panel24.ShadowDecoration.Color = adjustedColor;

            guna2ProgressBar1.FillColor = adjustedColor;
            guna2ProgressBar1.ShadowDecoration.Color = adjustedColor;
            guna2ProgressBar2.FillColor = adjustedColor;
            guna2ProgressBar2.ShadowDecoration.Color = adjustedColor;
            guna2ProgressBar3.FillColor = adjustedColor;
            guna2ProgressBar3.ShadowDecoration.Color = adjustedColor;

            guna2ProgressBar4.ProgressColor = adjustedColor;
            guna2ProgressBar4.ProgressColor2 = adjustedColor;
            guna2ProgressBar4.ShadowDecoration.Enabled = true;
            guna2ProgressBar4.ShadowDecoration.Color = adjustedColor;

            guna2Panel1.Refresh();
            guna2Panel2.Refresh();
            guna2Panel3.Refresh();

            guna2Panel30.ShadowDecoration.Color = adjustedColor;

            guna2Panel25.ShadowDecoration.Color = adjustedColor;

            label27.ForeColor = adjustedColor;
            label49.Text = $"{selectedColor.R}, {selectedColor.G}, {selectedColor.B}";

            // Chiamata ai metodi di aggiornamento delle immagini e dei colori
            UpdatePictureBoxColor();
            UpdateButtonImageColor();
            UpdateButtonImageColor1();
            UpdateButtonImageColor2();
            UpdatePictureBoxColor1();
            UpdatePictureBoxColor2();
            UpdatePictureBoxColor3();
            UpdatePictureBoxColor4();
            UpdatePictureBoxColor5();
            UpdatePictureBoxColor6();
            UpdateColorPanel24();

        }


        private void UpdatePictureBoxColor()
        {
            if (originalPictureBoxImage != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage.Width, originalPictureBoxImage.Height);

                for (int x = 0; x < originalPictureBoxImage.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2Button2.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor1()
        {
            if (originalPictureBoxImage2 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage2.Width, originalPictureBoxImage2.Height);

                for (int x = 0; x < originalPictureBoxImage2.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage2.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage2.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox2.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor4()
        {
            if (originalPictureBoxImage5 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage5.Width, originalPictureBoxImage5.Height);

                for (int x = 0; x < originalPictureBoxImage5.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage5.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage5.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox5.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor3()
        {
            if (originalPictureBoxImage4 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage4.Width, originalPictureBoxImage4.Height);

                for (int x = 0; x < originalPictureBoxImage4.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage4.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage4.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox4.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor5()
        {
            if (originalPictureBoxImage6 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage6.Width, originalPictureBoxImage6.Height);

                for (int x = 0; x < originalPictureBoxImage6.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage6.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage6.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox6.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor6()
        {
            if (originalPictureBoxImage7 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage7.Width, originalPictureBoxImage7.Height);

                for (int x = 0; x < originalPictureBoxImage7.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage7.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage7.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox7.Image = newImage;
            }
        }
        private void UpdatePictureBoxColor2()
        {
            if (originalPictureBoxImage3 != null)
            {
                Bitmap newImage = new Bitmap(originalPictureBoxImage3.Width, originalPictureBoxImage3.Height);

                for (int x = 0; x < originalPictureBoxImage3.Width; x++)
                {
                    for (int y = 0; y < originalPictureBoxImage3.Height; y++)
                    {
                        Color pixelColor = originalPictureBoxImage3.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox3.Image = newImage;
            }
        }

        private void UpdateButtonImageColor()
        {
            if (originalButtonImage != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage.Width, originalButtonImage.Height);

                for (int x = 0; x < originalButtonImage.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage.Height; y++)
                    {
                        Color pixelColor = originalButtonImage.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2Button15.Image = newImage;
            }
        }
        private void UpdateButtonImageColor1()
        {
            if (originalButtonImage1 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage1.Width, originalButtonImage1.Height);

                for (int x = 0; x < originalButtonImage1.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage1.Height; y++)
                    {
                        Color pixelColor = originalButtonImage1.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2Button1.Image = newImage;
            }
        }

        private void UpdateButtonImageColor2()
        {
            if (originalButtonImage2 != null)
            {
                Bitmap newImage = new Bitmap(originalButtonImage2.Width, originalButtonImage2.Height);

                for (int x = 0; x < originalButtonImage2.Width; x++)
                {
                    for (int y = 0; y < originalButtonImage2.Height; y++)
                    {
                        Color pixelColor = originalButtonImage2.GetPixel(x, y);

                        // Se il pixel è bianco o quasi bianco, lo sostituiamo con il colore selezionato
                        if (pixelColor.R > 200 && pixelColor.G > 200 && pixelColor.B > 200)
                        {
                            newImage.SetPixel(x, y, selectedColor);
                        }
                        else
                        {
                            newImage.SetPixel(x, y, pixelColor);
                        }
                    }
                }

                guna2PictureBox1.Image = newImage;
            }
        }

        private void guna2Button15_Click(object sender, EventArgs e)
        {
            Console.WriteLine("Button clicked");

            guna2Panel1.Visible = false;
            guna2Panel2.Visible = false;
            guna2Panel3.Visible = false;
            guna2Panel23.Visible = false;

            guna2ProgressBar1.ShadowDecoration.Enabled = true;
            guna2Button15.ShadowDecoration.Enabled = true;
            guna2Button1.ShadowDecoration.Enabled = false;
            guna2Button2.ShadowDecoration.Enabled = false;

            guna2ProgressBar3.Visible = false;
            guna2ProgressBar2.Visible = false;
            guna2ProgressBar1.Visible = true;

            guna2Panel16.Visible = false;
            guna2Panel12.Visible = false;
            guna2Panel7.Visible = true;

            Console.WriteLine("Visibility changes done");
        }


        private void guna2Button2_Click(object sender, EventArgs e)
        {
            guna2Panel1.Visible = false;
            guna2Panel2.Visible = false;
            guna2Panel3.Visible = false;
            guna2Panel23.Visible = false;
            guna2Button15.ShadowDecoration.Enabled = false;
            guna2Button1.ShadowDecoration.Enabled = false;
            guna2Button2.ShadowDecoration.Enabled = true;
            guna2ProgressBar3.Visible = false;
            guna2ProgressBar2.Visible = true;
            guna2ProgressBar1.Visible = false;
            guna2Panel7.Visible = false;
            guna2Panel16.Visible = false;
            guna2Panel12.Visible = true;
            guna2Panel12.Location = guna2Panel7.Location; // Assic
        }

        private void guna2Button1_Click(object sender, EventArgs e)
        {
            guna2ProgressBar3.Visible = true;
            guna2ProgressBar2.Visible = false;
            guna2ProgressBar1.Visible = false;
            guna2Button15.ShadowDecoration.Enabled = false;
            guna2Button1.ShadowDecoration.Enabled = true;
            guna2Button2.ShadowDecoration.Enabled = false;
            guna2Panel7.Visible = false;
            guna2Panel12.Visible = false;
            guna2Panel16.Visible = true;
            guna2Panel16.Location = guna2Panel7.Location;
            label62.Text = "Logs puliti...";
        }


        private void Guna2GradientPanel10_MouseDown(object sender, MouseEventArgs e)
        {
            isDragging = true;
            lastMousePosition = e.Location;
        }

        private void Guna2GradientPanel10_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        private void Guna2GradientPanel10_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                var deltaX = e.X - lastMousePosition.X;
                var deltaY = e.Y - lastMousePosition.Y;
                this.Location = new Point(this.Left + deltaX, this.Top + deltaY);
            }
        }
        private void Guna2GradientPanel4_MouseDown(object sender, MouseEventArgs e)
        {
            isDragging = true;
            lastMousePosition = e.Location;
        }

        private void Guna2GradientPanel4_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        private void Guna2GradientPanel4_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                var deltaX = e.X - lastMousePosition.X;
                var deltaY = e.Y - lastMousePosition.Y;
                this.Location = new Point(this.Left + deltaX, this.Top + deltaY);
            }
        }
        private Point lastMousePosition;
        private void Form5_Load(object sender, EventArgs e)
        {
            this.guna2Panel10.MouseDown += Guna2GradientPanel10_MouseDown;
            this.guna2Panel10.MouseUp += Guna2GradientPanel10_MouseUp;
            this.guna2Panel10.MouseMove += Guna2GradientPanel10_MouseMove;
            guna2Button10.Visible = false;
            this.guna2Panel4.MouseDown += Guna2GradientPanel4_MouseDown;
            this.guna2Panel4.MouseUp += Guna2GradientPanel4_MouseUp;
            this.guna2Panel4.MouseMove += Guna2GradientPanel4_MouseMove;
            //guna2Panel2.BringToFront();
            //guna2Panel2.BringToFront();
            //guna2Panel3.BringToFront();
            this.FormBorderStyle = FormBorderStyle.None; // Rimuove il bordo
            this.StartPosition = FormStartPosition.CenterScreen; // Posiziona al cent
            guna2ProgressBar3.Visible = false;
            guna2ProgressBar2.Visible = false;
            guna2ProgressBar1.Visible = true;
            guna2Panel21.Visible = false;
            //guna2Panel21.ShadowDecoration.Parent = this;
            //guna2Panel21.BringToFront();
            guna2Panel6.Padding = new Padding(10);
            this.Size = new Size(874, 566);
        }

        private void guna2Panel21_Paint(object sender, PaintEventArgs e)
        {

        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            guna2ProgressBar4.Increment(5);
            if (guna2ProgressBar4.Value >= guna2ProgressBar4.Maximum)
            {
                timer1.Stop();
                guna2ProgressBar4.Value = 0; // Reset della progress bar
            }
        }
        private void start()
        {
            guna2Panel21.Visible = true;
            guna2Panel21.Location = new Point(548, 439);
            timer1.Start();
            //guna2ProgressBar4.ProgressColor = Color.Red;
            //guna2ProgressBar4.ProgressColor2 = Color.Red;
            //guna2ProgressBar4.ShadowDecoration.Enabled = true;
            //guna2ProgressBar4.ShadowDecoration.Color = Color.Red;
            //guna2Panel25.ShadowDecoration.Color = Color.Red;
            //label27.ForeColor = Color.Red;
            label26.Text = "Error Try Agyn";
            guna2ProgressBar4.Value = 0; // Reset della progress bar
        }
        private async void guna2ToggleSwitch7_CheckedChanged(object sender, EventArgs e)
        {
            start();
        }
        private void eror1()
        {
            guna2Panel21.Visible = true;
            guna2Panel21.Location = new Point(548, 439);
            timer1.Start();
            //guna2ProgressBar4.ProgressColor = Color.Green;
            //guna2ProgressBar4.ProgressColor2 = Color.Green;
            //guna2ProgressBar4.ShadowDecoration.Enabled = true;
            //guna2ProgressBar4.ShadowDecoration.Color = Color.Green;
            //guna2Panel25.ShadowDecoration.Color = Color.Green;
            //label27.ForeColor = Color.Green;

            label26.Text = "Enable Successfulyy";

            guna2ProgressBar4.Value = 0; // Reset della progress bar
        }
        private void HideError1()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(HideError1));
            }
            else
            {
                guna2Panel21.Visible = false;
            }
        }
        private void waiting()
        {
            guna2Panel21.Visible = true;
            guna2Panel21.Location = new Point(548, 439);
            timer1.Start();
            //guna2ProgressBar4.ProgressColor = Color.Yellow;
            //guna2ProgressBar4.ProgressColor2 = Color.Yellow;
            //guna2ProgressBar4.ShadowDecoration.Enabled = true;
            //guna2ProgressBar4.ShadowDecoration.Color = Color.Yellow;

            //label27.ForeColor = Color.White;

            label26.Text = "Waitting....";

            guna2ProgressBar4.Value = 0; // Reset della progress bar
        }
        private void Actived()
        {
            guna2Panel21.Visible = true;
            guna2Panel21.Location = new Point(548, 439);
            timer1.Start();
            //guna2ProgressBar4.ProgressColor = Color.Yellow;
            //guna2ProgressBar4.ProgressColor2 = Color.Yellow;
            //guna2ProgressBar4.ShadowDecoration.Enabled = true;
            //guna2ProgressBar4.ShadowDecoration.Color = Color.Yellow;

            //label27.ForeColor = Color.White;

            label26.Text = "Actived! Ghost Hack";

            guna2ProgressBar4.Value = 0; // Reset della progress bar
        }
        private void Disaktived()
        {
            guna2Panel21.Visible = true;
            guna2Panel21.Location = new Point(548, 439);
            timer1.Start();
            //guna2ProgressBar4.ProgressColor = Color.Yellow;
            //guna2ProgressBar4.ProgressColor2 = Color.Yellow;
            //guna2ProgressBar4.ShadowDecoration.Enabled = true;
            //guna2ProgressBar4.ShadowDecoration.Color = Color.Yellow;

            //label27.ForeColor = Color.White;

            label26.Text = "Deactivated! Ghost Hack";

            guna2ProgressBar4.Value = 0; // Reset della progress bar
        }
        private async void guna2ToggleSwitch6_CheckedChanged(object sender, EventArgs e)
        {
            eror1();
        }

        private void guna2Panel6_Paint(object sender, PaintEventArgs e)
        {
            //guna2Panel21.ShadowDecoration.Enabled = true;
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel6);
        }

        private void guna2ToggleSwitch5_CheckedChanged(object sender, EventArgs e)
        {
            waiting();
        }

        private async void guna2ToggleSwitch4_CheckedChanged(object sender, EventArgs e)
        {
            //string search = "? ? ? 3F 00 00 80 3E 00 00 00 00 ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? ? 00 00 00 00 00 00 00 00 00 00 80 3F";
            //string replace = "EC 51 B8 3D 8F C2 F5 3C";

            //bool k = false;

            //if (Process.GetProcessesByName("HD-Player").Length == 0)
            //{
            //    start();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi

            //    Console.Beep(240, 300);
            //}

            //else
            //{
            //    memory.OpenProcess("HD-Player");
            //    waiting();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    int i2 = 22000000;
            //    IEnumerable<long> wl = await memory.AoBScan(search, writable: true);
            //    string u = "0x" + wl.FirstOrDefault().ToString("X");
            //    if (wl.Count() != 0)
            //    {
            //        for (int i = 0; i < wl.Count(); i++)
            //        {
            //            i2++;
            //            memory.WriteMemory(wl.ElementAt(i).ToString("X"), "bytes", replace);
            //        }
            //        k = true;
            //    }

            //    if (k == true)
            //    {
            //        start();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //        Console.Beep(400, 300);
            //    }
            //    else
            //    {
            //        eror1();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    }
            //}
        }
        private void SetActivePictureBox(PictureBox targetPictureBox, Action<Color> updateConfigAction)
        {
            // Applica l'azione per configurare il colore (ad esempio Config.ESPLineColor)
            updateConfigAction.Invoke(targetPictureBox.BackColor);

            // Configura ShadowDecoration (assicurati che il PictureBox abbia un controllo genitore compatibile)
            if (targetPictureBox.Parent is Guna.UI2.WinForms.Guna2Panel guna2Panel)
            {
                guna2Panel.ShadowDecoration.Enabled = true;

            }

            // Memorizza il PictureBox attivo e l'azione per aggiornare la configurazione
            activePictureBox = targetPictureBox;
            activeConfigUpdateAction = updateConfigAction;

        }
        // Variabile per tenere traccia dello stato del pannello
        private PictureBox activePictureBox; // Per tracciare il PictureBox attivo
        private Action<Color> activeConfigUpdateAction; // Per aggiornare la configurazione del colore
        private void UpdateColorConfig(Color color)
        {
            // Aggiorna il colore del PictureBox (ad esempio)
            pictureBox6.BackColor = color;
        }
        private void ShowColorSelector(PictureBox targetPictureBox)
        {
            // Impostiamo la visibilità di guna2Panel5, ColorBase e ColorP
            guna2Panel1.Visible = true;
            guna2Panel2.Visible = true;
            guna2Panel3.Visible = true;
            guna2Panel23.Visible = true;
            // Portiamo guna2Panel2 in primo piano
            //guna2Panel2.BringToFront();
            //guna2Panel2.BringToFront();
            //guna2Panel3.BringToFront();
            // Posizioniamo il guna2Panel2 nella posizione desiderata
            guna2Panel23.Location = new Point(508, 338);
        }
        private void pictureBox6_Click(object sender, EventArgs e)
        {
            // Mostra il selettore di colore
            ShowColorSelector(pictureBox6);
            // Configura il PictureBox attivo e aggiorna il colore nella configurazione
            SetActivePictureBox(pictureBox6, UpdateColorConfig);

        }

        private void guna2Panel1_Paint_1(object sender, PaintEventArgs e)
        {

        }



        private void guna2Panel14_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel14);
        }

        private void guna2Panel22_Paint_1(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox8_Click(object sender, EventArgs e)
        {
            eror1();
            Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
        }

        private void guna2CustomCheckBox5_Click(object sender, EventArgs e)
        {
            waiting();
            Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
        }

        private void guna2CustomCheckBox7_Click(object sender, EventArgs e)
        {
            start();
            Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
        }

        private void guna2Panel5_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel5);
        }

        private void guna2Panel10_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel10);
        }

        private void guna2CustomCheckBox19_Click(object sender, EventArgs e)
        {

        }

        private void guna2Panel7_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel7);
        }

        private void guna2Panel16_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel16);
        }

        private void guna2Panel17_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel17);
        }

        private void guna2Panel19_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel19);
        }

        private void guna2Panel12_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel12);
        }

        private void guna2Panel11_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel11);
        }

        private void guna2Panel4_Paint(object sender, PaintEventArgs e)
        {
            if (particlesEnabled)
                PaintParticlesOnPanel(e, guna2Panel4);
        }

        private void guna2Panel24_Paint_1(object sender, PaintEventArgs e)
        {

        }
        private IntPtr SetHook3(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                IntPtr moduleHandle = curModule.BaseAddress; // Ottieni l'indirizzo del modulo
                return SetWindowsHookEx(WH_KEYBOARD_LL3, proc, moduleHandle, 0);
            }
        }
        private const int WH_KEYBOARD_LL3 = 13;
        private const int WM_KEYDOWN3 = 0x0100;
        private IntPtr HookCallback3(int nCode3, IntPtr wParam3, IntPtr lParam3)
        {
            if (nCode3 >= 0 && wParam3 == (IntPtr)WM_KEYDOWN3)
            {
                KeysConverter keyConverter = new KeysConverter();
                string keyName = keyConverter.ConvertToString((Keys)Marshal.ReadInt32(lParam3));

                if (waitPressKey3)
                {
                    // Se viene premuto un tasto
                    if (keyName.Equals("Escape"))
                    {
                        guna2Button5.Text = "None"; // Se premi Escape
                    }
                    else
                    {
                        // Assicurati che guna2Button9 non sia null
                        if (guna2Button5 != null)
                        {
                            guna2Button5.Text = keyName; // Altrimenti aggiorna con il nome del tasto
                        }
                        else
                        {
                            Console.WriteLine("guna2Button10 è null!"); // Messaggio di debug
                        }
                    }
                    waitPressKey3 = false; // Resetta waitPressKey2
                }
                else
                {
                    // Logica per gestire il tasto premuto se non stiamo aspettando un input
                    string buttonText = guna2Button5.Text.Replace("...", "").Trim(); // Pulisce eventuali "..." e spazi
                    if (!string.IsNullOrEmpty(buttonText)) // Assicurati che il testo non sia vuoto
                    {
                        // Converte il testo in un valore di enum valido, altrimenti imposta un valore predefinito
                        Keys binding;
                        if (Enum.TryParse(buttonText, out binding) && binding != Keys.None)
                        {
                            // Controlla se il tasto premuto corrisponde al tasto scelto
                            if ((Keys)Marshal.ReadInt32(lParam3) == binding)
                            {
                                // Simula il click sul guna2Button11
                                guna2Button9.PerformClick(); // Esegui l'evento Click di guna2Button11
                            }
                        }
                        else
                        {
                            Console.WriteLine($"Il tasto '{buttonText}' non è valido come valore di enum.");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Testo del bottone non valido.");
                    }
                }
            }
            return CallNextHookEx(hookID3, nCode3, wParam3, lParam3);
        }
        // ID dell'hook (modificato a 3)
        IntPtr hookID3 = IntPtr.Zero;
        private LowLevelKeyboardProc hookCallback3;
        private void guna2Button5_Click(object sender, EventArgs e)
        {
            waitPressKey3 = true;
            // Chiama il metodo per attendere un tasto
            guna2Button5.Text = "..."; // Indica che si sta aspettando un tasto

        }


        private void guna2Button9_Click(object sender, EventArgs e)
        {
            // Nascondi o mostra Form1 (this)
            if (this.Visible)
            {
                this.Hide();
            }
            else
            {
                this.Show();
            }
        }

        private void guna2Panel25_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Button11_Click(object sender, EventArgs e)
        {

        }

        private void guna2CustomCheckBox20_Click(object sender, EventArgs e)
        {
            waiting();
        }

        private void guna2Panel22_Paint_2(object sender, PaintEventArgs e)
        {

        }

        private void guna2PictureBox1_Click(object sender, EventArgs e)
        {

        }

        private void guna2Panel28_Paint(object sender, PaintEventArgs e)
        {
            e.Graphics.DrawImage(colorGradient22, 0, 0, guna2Panel28.Width, guna2Panel28.Height);
            DrawSelector22(e.Graphics, colorSelectorPosition22);
        }

        private void guna2Panel29_Paint(object sender, PaintEventArgs e)
        {
            e.Graphics.DrawImage(brightnessGradient22, 0, 0, guna2Panel29.Width, guna2Panel29.Height);
            DrawSelector22(e.Graphics, brightnessSelectorPosition22); // Ora il selettore si muove
        }

        private void guna2PictureBox8_Click(object sender, EventArgs e)
        {
            // Impostiamo il target attuale su PictureBox8
            currentTarget = TargetImage.PictureBox8;

            // Mostriamo il selettore
            guna2Panel27.Visible = true;
            guna2Panel28.Visible = true;
            guna2Panel29.Visible = true;

            // Posizioniamo il selettore nella posizione desiderata
            guna2Panel27.Location = new Point(391, 104);

            // Configuriamo per modificare il colore di Config.ESPLine
            targetColor = colorGradient22.GetPixel(colorSelectorPosition22.X, colorSelectorPosition22.Y);
            Config.ESPLine = true;
            colorUpdateTimer.Start();
        }
        private Color GetAverageColor(Bitmap bmp)
        {
            int r = 0, g = 0, b = 0, totalPixels = 0;

            for (int x = 0; x < bmp.Width; x++)
            {
                for (int y = 0; y < bmp.Height; y++)
                {
                    Color pixelColor = bmp.GetPixel(x, y);
                    r += pixelColor.R;
                    g += pixelColor.G;
                    b += pixelColor.B;
                    totalPixels++;
                }
            }

            return Color.FromArgb(r / totalPixels, g / totalPixels, b / totalPixels);
        }


        private void guna2PictureBox9_Click(object sender, EventArgs e)
        {
            // Impostiamo il target attuale su PictureBox9
            currentTarget = TargetImage.PictureBox9;

            // Mostriamo il selettore
            guna2Panel27.Visible = true;
            guna2Panel28.Visible = true;
            guna2Panel29.Visible = true;

            // Posizioniamo il selettore nella posizione desiderata
            guna2Panel27.Location = new Point(391, 104);

            // Configuriamo per modificare il colore di Config.ESPBox
            targetColor = colorGradient22.GetPixel(colorSelectorPosition22.X, colorSelectorPosition22.Y);
            Config.ESPBox = true;
            colorUpdateTimer.Start();
        }


        private void guna2PictureBox10_Click(object sender, EventArgs e)
        {
            // Impostiamo il target attuale su PictureBox9
            currentTarget = TargetImage.PictureBox10;

            // Mostriamo il selettore
            guna2Panel27.Visible = true;
            guna2Panel28.Visible = true;
            guna2Panel29.Visible = true;

            // Posizioniamo il selettore nella posizione desiderata
            guna2Panel27.Location = new Point(391, 104);
        }

        private void guna2PictureBox11_Click(object sender, EventArgs e)
        {
            // Impostiamo il target attuale su PictureBox9
            currentTarget = TargetImage.PictureBox11;
            // Configuriamo per modificare il colore di Config.ESPLine
            targetColor = colorGradient22.GetPixel(colorSelectorPosition22.X, colorSelectorPosition22.Y);
            Config.ESPSkeleton = true;
            colorUpdateTimer.Start();
            // Mostriamo il selettore
            guna2Panel27.Visible = true;
            guna2Panel28.Visible = true;
            guna2Panel29.Visible = true;

            // Posizioniamo il selettore nella posizione desiderata
            guna2Panel27.Location = new Point(391, 104);
        }

        private void guna2Panel27_Paint(object sender, PaintEventArgs e)
        {

        }
        // Variabili globali per search e replace
        string search5 = "00 00 7A 44 40 09 54 06 F0 4F 2D E9 1C B0 8D E2 A4 D0 4D E2 00 60 A0 E1 6C 03 9F E5 02 70 A0 E1 01 40 A0 E1 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00";
        string replace5 = "00 00 7A 00 40 09 54 06 F0 4F 2D E9 1C B0 8D E2 A4 D0 4D E2 00 60 A0 E1 6C 03 9F E5 02 70 A0 E1 01 40 A0 E1 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00";

        string search6 = "00 00 7A 00 40 09 54 06 F0 4F 2D E9 1C B0 8D E2 A4 D0 4D E2 00 60 A0 E1 6C 03 9F E5 02 70 A0 E1 01 40 A0 E1 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00";
        string replace6 = "00 00 7A 44 40 09 54 06 F0 4F 2D E9 1C B0 8D E2 A4 D0 4D E2 00 60 A0 E1 6C 03 9F E5 02 70 A0 E1 01 40 A0 E1 00 00 8F E0 00 00 D0 E5 00 00 50 E3 06 00";

        // Liste per memorizzare gli indirizzi trovati (globali)
        List<long> addresses5 = new List<long>(); // Indirizzi per l'attivazione
        List<long> addresses6 = new List<long>(); // Indirizzi per la disattivazione

        private async void guna2CustomCheckBox15_Click(object sender, EventArgs e)
        {
            //// Imposta il checkbox come selezionato
            //guna2CustomCheckBox15.Checked = true;
            //if (Process.GetProcessesByName("HD-Player").Length == 0)
            //{

            //}

            //// Questo codice sarà eseguito ogni volta, anche se i processi sono già aperti
            //memory.OpenProcess("HD-Player");

            //// Scansione per l'attivazione (search1) - senza il controllo che impedisce la scansione se gli indirizzi sono già stati trovati
            //// Esegui la scansione e salva gli indirizzi per l'attivazione
            //IEnumerable<long> wl1 = await memory.AoBScan(search5, writable: true);
            //if (wl1.Count() != 0)
            //{
            //    addresses5 = wl1.ToList(); // Memorizza gli indirizzi per l'attivazione

            //    // Scrivi direttamente la modifica nella memoria per l'attivazione
            //    foreach (var address in addresses5)
            //    {
            //        memory.WriteMemory(address.ToString("X"), "bytes", replace5);
            //    }

            //    eror1();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //}
            //else
            //{
            //    start();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //}

            //// Scansione per la disattivazione (search2) - anche questa eseguita ogni volta
            //// Esegui la scansione e salva gli indirizzi per la disattivazione
            //IEnumerable<long> wl2 = await memory.AoBScan(search6, writable: true);
            //if (wl2.Count() != 0)
            //{
            //    addresses6 = wl2.ToList(); // Memorizza gli indirizzi per la disattivazione

            //    // Scrivi direttamente la modifica nella memoria per la disattivazione
            //    foreach (var address in addresses6)
            //    {
            //        memory.WriteMemory(address.ToString("X"), "bytes", replace6);
            //    }

            //    Disaktived();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //}
            //else
            //{
            //    start();
            //    Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi

            //}
        }
        private IntPtr SetHook4(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                IntPtr moduleHandle = curModule.BaseAddress; // Ottieni l'indirizzo del modulo
                return SetWindowsHookEx(WH_KEYBOARD_LL3, proc, moduleHandle, 0);
            }
        }

        private IntPtr HookCallback4(int nCode4, IntPtr wParam4, IntPtr lParam4)
        {
            if (nCode4 >= 0 && wParam4 == (IntPtr)WM_KEYDOWN4)
            {
                KeysConverter keyConverter = new KeysConverter();
                string keyName = keyConverter.ConvertToString((Keys)Marshal.ReadInt32(lParam4));

                if (waitPressKey4)
                {
                    // Se viene premuto un tasto
                    if (keyName.Equals("Escape"))
                    {
                        guna2Button7.Text = "None"; // Se premi Escape
                    }
                    else
                    {
                        // Assicurati che guna2Button9 non sia null
                        if (guna2Button7 != null)
                        {
                            guna2Button7.Text = keyName; // Altrimenti aggiorna con il nome del tasto
                        }
                        else
                        {
                            Console.WriteLine("guna2Button10 è null!"); // Messaggio di debug
                        }
                    }
                    waitPressKey4 = false; // Resetta waitPressKey2
                }
                else
                {
                    // Logica per gestire il tasto premuto se non stiamo aspettando un input
                    string buttonText = guna2Button7.Text.Replace("...", "").Trim(); // Pulisce eventuali "..." e spazi
                    if (!string.IsNullOrEmpty(buttonText)) // Assicurati che il testo non sia vuoto
                    {
                        // Converte il testo in un valore di enum valido, altrimenti imposta un valore predefinito
                        Keys binding;
                        if (Enum.TryParse(buttonText, out binding) && binding != Keys.None)
                        {
                            // Controlla se il tasto premuto corrisponde al tasto scelto
                            if ((Keys)Marshal.ReadInt32(lParam4) == binding)
                            {
                                // Simula il click sul guna2Button11
                                guna2Button12.PerformClick(); // Esegui l'evento Click di guna2Button11
                            }
                        }
                        else
                        {
                            Console.WriteLine($"Il tasto '{buttonText}' non è valido come valore di enum.");
                        }
                    }
                    else
                    {
                        Console.WriteLine("Testo del bottone non valido.");
                    }
                }
            }
            return CallNextHookEx(hookID4, nCode4, wParam4, lParam4);
        }
        private const int WM_KEYDOWN4 = 0x0100;
        IntPtr hookID4 = IntPtr.Zero;
        private bool waitPressKey4 = false; private LowLevelKeyboardProc hookCallback4;
        private void guna2Button7_Click(object sender, EventArgs e)
        {
            waitPressKey4 = true; // Resetta waitPressKey2
            guna2Button7.Text = "..."; // Indica che si sta aspettando un tasto

        }
        private int clickCount = 0;
        private void guna2Button12_Click(object sender, EventArgs e)
        {
            //clickCount++;

            //if (Process.GetProcessesByName("HD-Player").Length == 0)
            //{

            //    return;
            //}

            //memory.OpenProcess("HD-Player");

            //if (clickCount % 2 == 1) // Click dispari: attiva
            //{
            //    if (addresses5.Any()) // Se gli indirizzi per l'attivazione sono già stati trovati
            //    {
            //        // Scrivi direttamente i valori senza effettuare una nuova scansione
            //        foreach (var address in addresses5)
            //        {
            //            memory.WriteMemory(address.ToString("X"), "bytes", replace5);
            //        }
            //        Actived();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    }
            //    else
            //    {
            //        start();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    }
            //}
            //else // Click pari: disattiva
            //{
            //    if (addresses6.Any()) // Se gli indirizzi per la disattivazione sono già stati trovati
            //    {
            //        // Scrivi direttamente i valori senza effettuare una nuova scansione
            //        foreach (var address in addresses6)
            //        {
            //            memory.WriteMemory(address.ToString("X"), "bytes", replace6);
            //        }

            //        Disaktived();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    }
            //    else
            //    {
            //        start();
            //        Task.Delay(1200).ContinueWith(_ => HideError1()); // Nasconde error1 dopo 3 secondi
            //    }
            //}
        }

        private void guna2CustomCheckBox1_Click(object sender, EventArgs e)
        {

            Config.AimBot = guna2CustomCheckBox1.Checked;
        }

        private void guna2CustomCheckBox9_Click(object sender, EventArgs e)
        {

            Config.ESPLine = guna2CustomCheckBox9.Checked;
            Config.ESPHealth = guna2CustomCheckBox9.Checked;
            Config.ESPWeapon = guna2CustomCheckBox9.Checked;

            // Assicurati che nel form sia abilitato
            Config.ESPWeapon = true;

        }


        private void guna2CustomCheckBox10_Click(object sender, EventArgs e)
        {
            Config.ESPBox = guna2CustomCheckBox10.Checked;
        }

        private void guna2CustomCheckBox11_Click(object sender, EventArgs e)
        {

            Config.ESPName = guna2CustomCheckBox11.Checked;
            Config.ESPWeapon = guna2CustomCheckBox11.Checked;
        }

        private void guna2CustomCheckBox12_Click(object sender, EventArgs e)
        {

            Config.ESPSkeleton = guna2CustomCheckBox12.Checked;

        }
        static IntPtr FindRenderWindow(IntPtr parent)
        {
            IntPtr renderWindow = IntPtr.Zero;
            WinAPI.EnumChildWindows(parent, (hWnd, lParam) =>
            {
                StringBuilder sb = new StringBuilder(256);
                WinAPI.GetWindowText(hWnd, sb, sb.Capacity);
                string windowName = sb.ToString();
                if (!string.IsNullOrEmpty(windowName))
                {
                    if (windowName != "HD-Player")
                    {
                        renderWindow = hWnd;
                    }
                }
                return true;
            }, IntPtr.Zero);

            return renderWindow;
        }
        IntPtr mainHandle;
        private async void guna2Button18_Click(object sender, EventArgs e)
        {

            // Cambia el texto y color inicial del botón
            guna2Button18.FillColor = Color.Yellow;
            guna2Button18.Text = "Conectando...";

            var processes = Process.GetProcessesByName("HD-Player");

            if (processes.Length != 1)
            {
                guna2Button18.FillColor = Color.Red;
                guna2Button18.Text = "Error: Servidor no encontrado";
                return;
            }

            var process = processes[0];
            var mainModulePath = Path.GetDirectoryName(process.MainModule.FileName);
            var adbPath = Path.Combine(mainModulePath, "HD-Adb.exe");

            if (!File.Exists(adbPath))
            {
                guna2Button18.FillColor = Color.Red;
                guna2Button18.Text = "Error: Archivo no encontrado";
                return;
            }

            var adb = new Adb(adbPath);
            await adb.Kill();

            var started = await adb.Start();
            if (!started)
            {
                guna2Button18.FillColor = Color.Red;
                guna2Button18.Text = "Error: No se pudo iniciar";
                return;
            }

            String pkg = "com.dts.freefireth";
            String lib = "libil2cpp.so";

            bool isFreeFireMax = false;
            if (isFreeFireMax)
            {
                pkg = "com.dts.freefiremax";
            }

            var moduleAddr = await adb.FindModule(pkg, lib);
            if (moduleAddr == 0) // Si no encuentra la dirección del módulo
            {
                guna2Button18.FillColor = Color.Red;
                guna2Button18.Text = "Error: Módulo no encontrado";
                return;
            }

            Offsets.Il2Cpp = moduleAddr;
            Core.Handle = FindRenderWindow(mainHandle);




            new Thread(Data.Work) { IsBackground = true }.Start();
            new Thread(Aimbot.Work) { IsBackground = true }.Start();

            // Cambia el texto y color final del botón
            guna2Button18.FillColor = Color.LimeGreen;
            guna2Button18.Text = "Conectado";




        }

        private void guna2ComboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //if (guna2ComboBox1.Text == "Top")
            //{
            //    Config.ESPLinePosition = LinePosition.Top;
            //}
            //if (guna2ComboBox1.Text == "Bottom")
            //{
            //    Config.ESPLinePosition = LinePosition.Bottom;
            //}
            //if (guna2ComboBox1.Text == "Center")
            //{
            //    Config.ESPLinePosition = LinePosition.Center;
            //}
        }
        [DllImport("user32.dll")]
        static extern uint SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);

        const uint WDA_NONE = 0x00000000;
        const uint WDA_MONITOR = 0x00000001;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        private void guna2CustomCheckBox18_Click(object sender, EventArgs e)
        {
            if (guna2CustomCheckBox18.Checked)

                SetStreamMode(guna2CustomCheckBox18.Checked);
            Config.StreamMode = guna2CustomCheckBox18.Checked;

            void SetStreamMode(bool state)
            {
                foreach (var obj in Application.OpenForms)
                {
                    var form = obj as Form;

                    if (state)
                    {
                        SetWindowDisplayAffinity(form.Handle, WDA_EXCLUDEFROMCAPTURE);

                    }
                    else
                    {

                        SetWindowDisplayAffinity(form.Handle, WDA_NONE);

                    }
                }
            }
        }

        private void timer4_Tick(object sender, EventArgs e)
        {
            InternalMemory.Cache = new();
            Core.Entities = new();
        }

        private void guna2Panel30_Paint_1(object sender, PaintEventArgs e)
        {

        }

        private void guna2PictureBox7_Click(object sender, EventArgs e)
        {

        }

        private void guna2CustomCheckBox13_Click(object sender, EventArgs e)
        {
            if (guna2CustomCheckBox13.Checked)
            {
                UpdateESPColorsToWarm();
            }
            else
            {
                ResetESPColors();
            }
        }

        private void UpdateESPColorsToWarm()
        {
            // Aggiorna i colori delle varie parti dell'ESP con colori caldi casuali
            Config.ESPLineColor = GetRandomWarmColor();
            Config.ESPSkeletonColor = GetRandomWarmColor();
            Config.ESPBoxColor = GetRandomWarmColor();
            Config.ESPNameColor = GetRandomWarmColor();
        }

        private void ResetESPColors()
        {
            // Reimposta i colori ai valori predefiniti
            Config.ESPLineColor = Color.White;
            Config.ESPSkeletonColor = Color.White;
            Config.ESPBoxColor = Color.White;
            Config.ESPNameColor = Color.White;
        }

        private Color GetRandomWarmColor()
        {
            int index = random.Next(warmColors.Length);
            return warmColors[index];
        }

        private void guna2Button4_Click(object sender, EventArgs e)
        {

        }

        private void guna2CustomCheckBox20_Click_1(object sender, EventArgs e)
        {

            Config.AimbotSilent = guna2CustomCheckBox20.Checked;
            Config.NoRecoil = guna2CustomCheckBox20.Checked;
        }

        private void guna2CustomCheckBox2_Click(object sender, EventArgs e)
        {
            Config.ESPWeapon = guna2CustomCheckBox2.Checked;
            Config.ESPWeaponIcon = guna2CustomCheckBox2.Checked;
        }

        private void guna2Panel3_Paint_1(object sender, PaintEventArgs e)
        {

        }

        private void guna2Button3_Click(object sender, EventArgs e)
        {

        }
    }
}

    
