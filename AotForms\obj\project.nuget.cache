{"version": 2, "dgSpecHash": "r3Sxk3XF8y4=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Downloads\\ESP LINE\\AotForms\\Client.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\clickabletransparentoverlay\\9.3.0\\clickabletransparentoverlay.9.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\costura.fody\\6.0.0\\costura.fody.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnne\\2.0.6\\dnne.2.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fody\\6.8.2\\fody.6.8.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\guna.ui2.winforms\\2.0.4.6\\guna.ui2.winforms.2.0.4.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\imgui.net\\1.90.1.1\\imgui.net.1.90.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.dotnet.ilcompiler\\7.0.20\\microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.illink.analyzers\\7.0.100-1.23211.1\\microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.illink.tasks\\7.0.100-1.23211.1\\microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mousekeyhook\\5.7.1\\mousekeyhook.5.7.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpgen.runtime\\2.1.2-beta\\sharpgen.runtime.2.1.2-beta.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpgen.runtime.com\\2.1.2-beta\\sharpgen.runtime.com.2.1.2-beta.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\3.1.2\\sixlabors.imagesharp.3.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.4.0\\system.buffers.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.0\\system.management.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.4.0\\system.numerics.vectors.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.4.0\\system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vortice.d3dcompiler\\3.3.4\\vortice.d3dcompiler.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vortice.direct3d11\\3.3.4\\vortice.direct3d11.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vortice.directx\\3.3.4\\vortice.directx.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vortice.dxgi\\3.3.4\\vortice.dxgi.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vortice.mathematics\\1.7.2\\vortice.mathematics.1.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\winformscominterop\\0.5.0\\winformscominterop.0.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\7.0.20\\microsoft.windowsdesktop.app.ref.7.0.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.microsoft.dotnet.ilcompiler\\7.0.20\\runtime.win-x64.microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\7.0.20\\microsoft.netcore.app.ref.7.0.20.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\7.0.20\\microsoft.aspnetcore.app.ref.7.0.20.nupkg.sha512"], "logs": []}