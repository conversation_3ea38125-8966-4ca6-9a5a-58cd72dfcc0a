﻿using AotForms;
using System;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;

namespace Client
{
    internal static class FlyMe
    {
        private static Task upTask;
        private static CancellationTokenSource cts = new();
        private static bool isRunning = false;

        //BY SHUBHAMBXQ
        private const float TargetY = 20f;

        internal static void Work()
        {
            upTask = Task.Run(async () =>
            {
                while (!cts.Token.IsCancellationRequested)
                {
                    try
                    {
                        if (!Config.flyme)
                        {
                            await Task.Delay(10, cts.Token);
                            continue;
                        }

                        if (!InternalMemory.Read(Core.LocalPlayer + (uint)Bones.Root, out uint rootBonePtr) || rootBonePtr == 0) continue;
                        if (!InternalMemory.Read(rootBonePtr + 0x8, out uint transform1) || transform1 == 0) continue;
                        if (!InternalMemory.Read(transform1 + 0x8, out uint transform2) || transform2 == 0) continue;
                        if (!InternalMemory.Read(transform2 + 0x20, out uint matrixPtr) || matrixPtr == 0) continue;

                        if (!InternalMemory.Read(matrixPtr + 0x80, out Vector3 currentPos)) continue;

                        
                        currentPos.Y -= 0.5f; 

                        InternalMemory.Write(matrixPtr + 0x80, currentPos);
                    }
                    catch
                    {
                        
                    }

                    await Task.Delay(10, cts.Token);
                }
            }, cts.Token);
        }

        internal static void Stop()
        {
            if (!isRunning) return;

            cts.Cancel();
            cts.Dispose();
            cts = new CancellationTokenSource();
            isRunning = false;
        }
    }
}
