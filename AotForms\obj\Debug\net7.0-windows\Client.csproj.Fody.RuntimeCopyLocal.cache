C:\Users\<USER>\.nuget\packages\clickabletransparentoverlay\9.3.0\lib\net7.0\ClickableTransparentOverlay.dll
C:\Users\<USER>\.nuget\packages\guna.ui2.winforms\2.0.4.6\lib\net7.0-windows7.0\Guna.UI2.dll
C:\Users\<USER>\.nuget\packages\imgui.net\1.90.1.1\lib\net6.0\ImGui.NET.dll
C:\Users\<USER>\.nuget\packages\mousekeyhook\5.7.1\lib\net7.0-windows7.0\Gma.System.MouseKeyHook.dll
C:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.3\lib\net6.0\Newtonsoft.Json.dll
C:\Users\<USER>\.nuget\packages\sharpgen.runtime\2.1.2-beta\lib\net7.0\SharpGen.Runtime.dll
C:\Users\<USER>\.nuget\packages\sharpgen.runtime.com\2.1.2-beta\lib\net7.0\SharpGen.Runtime.COM.dll
C:\Users\<USER>\.nuget\packages\sixlabors.imagesharp\3.1.2\lib\net6.0\SixLabors.ImageSharp.dll
C:\Users\<USER>\.nuget\packages\system.codedom\7.0.0\lib\net7.0\System.CodeDom.dll
C:\Users\<USER>\.nuget\packages\system.management\7.0.0\lib\net7.0\System.Management.dll
C:\Users\<USER>\.nuget\packages\system.runtime.compilerservices.unsafe\4.4.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\vortice.d3dcompiler\3.3.4\lib\net7.0\Vortice.D3DCompiler.dll
C:\Users\<USER>\.nuget\packages\vortice.direct3d11\3.3.4\lib\net7.0\Vortice.Direct3D11.dll
C:\Users\<USER>\.nuget\packages\vortice.directx\3.3.4\lib\net7.0\Vortice.DirectX.dll
C:\Users\<USER>\.nuget\packages\vortice.dxgi\3.3.4\lib\net7.0\Vortice.DXGI.dll
C:\Users\<USER>\.nuget\packages\vortice.mathematics\1.7.2\lib\net7.0\Vortice.Mathematics.dll
C:\Users\<USER>\.nuget\packages\winformscominterop\0.5.0\lib\net7.0\WinFormsComInterop.dll
