﻿using AotForms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Client
{
    internal static class Aimbot
    {
        internal static void Work()
        {
            int errorCount = 0;
            const int maxErrors = 5;

            while (true)
            {
                try
                {
                    if (!Config.AimBot)
                    {
                        Thread.Sleep(PerformanceConfig.TaskDelayOnDisabled);
                        continue;
                    }

                    if ((WinAPI.GetAsyncKeyState(Config.AimbotKey) & 0x8000) == 0)
                    {
                        Thread.Sleep(PerformanceConfig.TaskDelayOnKeyNotPressed);
                        continue;
                    }

                Entity target = null;
                float distance = float.MaxValue;

                if (Core.Width == -1 || Core.Height == -1) continue;
                if (!Core.HaveMatrix) continue;

                var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

                foreach (var entity in Core.Entities.Values)
                {
                    if (entity.IsDead) continue;

                    var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                    var root2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                    if (head2D.X < 1 || head2D.Y < 1) continue;
                    if (root2D.X < 1 || root2D.Y < 1) continue;

                    var playerDistance = Vector3.Distance(Core.LocalMainCamera, entity.Head);

                    if (playerDistance > Config.AimBotMaxDistance) continue;

                    var x = head2D.X - screenCenter.X;
                    var y = head2D.Y - screenCenter.Y;
                    var crosshairDist = (float)Math.Sqrt(x * x + y * y);

                    if (crosshairDist >= distance || crosshairDist == float.MaxValue)
                    {
                        continue;
                    }

                    // Ridurre il FoV per rendere l'aimbot più preciso
                    if (crosshairDist > Config.AimBotFov / 2) // Regola il divisore per cambiare il FoV
                    {
                        continue;
                    }

                    distance = crosshairDist;
                    target = entity;
                }

                if (target != null)
                {
                    // Regolare la rotazione per mirare alla testa in modo più preciso
                    var playerLook = MathUtils.GetRotationToLocation(target.Head, Config.aimlegit, Core.LocalMainCamera);

                    InternalMemory.Write(Core.LocalPlayer + Offsets.AimRotation, playerLook);
                    Thread.Sleep(Config.aimdelay > 0 ? Config.aimdelay : 10);
                }

                // تحديث نبضة المراقبة
                ProcessMonitor.UpdateAimbotHeartbeat();

                // تأخير مناسب لتقليل استهلاك CPU
                Thread.Sleep(PerformanceConfig.GetTargetFrameTime(PerformanceConfig.AimbotTargetFPS));
                errorCount = 0; // إعادة تعيين عداد الأخطاء عند النجاح
                }
                catch (Exception ex)
                {
                    errorCount++;
                    ErrorHandler.LogError($"Aimbot.Work error (attempt {errorCount})", ex);

                    if (errorCount >= maxErrors)
                    {
                        ErrorHandler.LogError("Too many errors in Aimbot.Work, taking a break", ex);
                        errorCount = 0;
                        Thread.Sleep(5000);
                    }
                    else
                    {
                        Thread.Sleep(PerformanceConfig.TaskDelayOnError * errorCount);
                    }
                }
            }
        }
    }
}
