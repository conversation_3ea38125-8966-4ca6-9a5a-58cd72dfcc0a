﻿// Decompiled with JetBrains decompiler
// Type: PAIN3XMEM.pain3xmem
// Assembly: PAIN3XPREMIUM, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: A4668FF3-823C-4772-950D-300068930571
// Assembly location: C:\Users\<USER>\Downloads\svchost.exe

using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace Client
{
    public class pain3xmem
    {
        private bool _is64Bit;
        private Dictionary<string, IntPtr> modules = new Dictionary<string, IntPtr>();
        private ProcessModule mainModule;
        public Process theProc;
        private uint MEM_PRIVATE = 131072;
        private uint MEM_IMAGE = 16777216;
        public IntPtr pHandle;

        [DllImport("kernel32.dll")]
        private static extern void GetSystemInfo(out pain3xmem.SYSTEM_INFO lpSystemInfo);

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(
          uint dwDesiredAccess,
          bool bInheritHandle,
          int dwProcessId);

        [DllImport("kernel32")]
        public static extern bool IsWow64Process(IntPtr hProcess, out bool lpSystemInfo);

        [DllImport("kernel32.dll")]
        private static extern bool VirtualProtectEx(
          IntPtr hProcess,
          UIntPtr lpAddress,
          IntPtr dwSize,
          pain3xmem.MemoryProtection flNewProtect,
          out pain3xmem.MemoryProtection lpflOldProtect);

        [DllImport("kernel32.dll")]
        private static extern bool WriteProcessMemory(
          IntPtr hProcess,
          UIntPtr lpBaseAddress,
          byte[] lpBuffer,
          UIntPtr nSize,
          IntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(
          IntPtr hProcess,
          UIntPtr lpBaseAddress,
          [Out] byte[] lpBuffer,
          UIntPtr nSize,
          IntPtr lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        public static extern int CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", EntryPoint = "VirtualQueryEx")]
        public static extern UIntPtr Native_VirtualQueryEx(
          IntPtr hProcess,
          UIntPtr lpAddress,
          out pain3xmem.MEMORY_BASIC_INFORMATION64 lpBuffer,
          UIntPtr dwLength);

        [DllImport("kernel32.dll", EntryPoint = "VirtualQueryEx")]
        public static extern UIntPtr Native_VirtualQueryEx(
          IntPtr hProcess,
          UIntPtr lpAddress,
          out pain3xmem.MEMORY_BASIC_INFORMATION32 lpBuffer,
          UIntPtr dwLength);

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode)]
        private static extern uint GetPrivateProfileString(
          string lpAppName,
          string lpKeyName,
          string lpDefault,
          StringBuilder lpReturnedString,
          uint nSize,
          string lpFileName);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(
          IntPtr hProcess,
          UIntPtr lpBaseAddress,
          [Out] IntPtr lpBuffer,
          UIntPtr nSize,
          out ulong lpNumberOfBytesRead);

        public string LoadCode(string name, string file)
        {
            StringBuilder lpReturnedString = new StringBuilder(1024);
            if (file != "")
                pain3xmem.GetPrivateProfileString("codes", name, "", lpReturnedString, (uint)lpReturnedString.Capacity, file);
            else
                lpReturnedString.Append(name);
            return lpReturnedString.ToString();
        }

        public byte[] AhReadMeFucker(string code, long length, string file = "")
        {
            byte[] lpBuffer = new byte[length];
            return pain3xmem.ReadProcessMemory(this.pHandle, this.GetCode(code, file), lpBuffer, (UIntPtr)checked((ulong)length), IntPtr.Zero) ? lpBuffer : (byte[])null;
        }

        public Task<IEnumerable<long>> AoBScan(
          long start,
          long end,
          string search,
          bool readable,
          bool writable,
          bool executable,
          string file = "")
        {
            return Task.Run<IEnumerable<long>>((Func<IEnumerable<long>>)(() =>
            {
                List<MemoryRegionResult> source = new List<MemoryRegionResult>();
                string[] strArray = this.LoadCode(search, file).Split(' ');
                byte[] aobPattern = new byte[strArray.Length];
                byte[] mask = new byte[strArray.Length];
                for (int index = 0; index < strArray.Length; ++index)
                {
                    string str = strArray[index];
                    if (str == "??" || str.Length == 1 && str == "?")
                    {
                        mask[index] = (byte)0;
                        strArray[index] = "0x00";
                    }
                    else if (char.IsLetterOrDigit(str[0]) && str[1] == '?')
                    {
                        mask[index] = (byte)240;
                        strArray[index] = str[0].ToString() + "0";
                    }
                    else if (char.IsLetterOrDigit(str[1]) && str[0] == '?')
                    {
                        mask[index] = (byte)15;
                        strArray[index] = "0" + str[1].ToString();
                    }
                    else
                        mask[index] = byte.MaxValue;
                }
                for (int index = 0; index < strArray.Length; ++index)
                    aobPattern[index] = (byte)((uint)Convert.ToByte(strArray[index], 16) & (uint)mask[index]);
                pain3xmem.SYSTEM_INFO lpSystemInfo = new pain3xmem.SYSTEM_INFO();
                pain3xmem.GetSystemInfo(out lpSystemInfo);
                UIntPtr applicationAddress1 = lpSystemInfo.minimumApplicationAddress;
                UIntPtr applicationAddress2 = lpSystemInfo.maximumApplicationAddress;
                if (start < (long)applicationAddress1.ToUInt64())
                    start = (long)applicationAddress1.ToUInt64();
                if (end > (long)applicationAddress2.ToUInt64())
                    end = (long)applicationAddress2.ToUInt64();
                Debug.WriteLine("[DEBUG] memory scan starting... (start:0x" + start.ToString(this.MSize()) + " end:0x" + end.ToString(this.MSize()) + " time:" + DateTime.Now.ToString("h:mm:ss tt") + ")");
                UIntPtr lpAddress = new UIntPtr((ulong)start);
                pain3xmem.MEMORY_BASIC_INFORMATION lpBuffer = new pain3xmem.MEMORY_BASIC_INFORMATION();
                while (this.VirtualQueryEx(this.pHandle, lpAddress, out lpBuffer).ToUInt64() != 0UL && lpAddress.ToUInt64() < (ulong)end && lpAddress.ToUInt64() + (ulong)lpBuffer.RegionSize > lpAddress.ToUInt64())
                {
                    bool flag1 = ((lpBuffer.State == 4096U & lpBuffer.BaseAddress.ToUInt64() < applicationAddress2.ToUInt64() & ((int)lpBuffer.Protect & 256) == 0 & ((int)lpBuffer.Protect & 1) == 0 ? 1 : 0) & ((int)lpBuffer.Type == (int)this.MEM_PRIVATE ? 1 : ((int)lpBuffer.Type == (int)this.MEM_IMAGE ? 1 : 0))) != 0;
                    if (flag1)
                    {
                        bool flag2 = (lpBuffer.Protect & 2U) > 0U;
                        bool flag3 = (lpBuffer.Protect & 4U) > 0U || (lpBuffer.Protect & 8U) > 0U || (lpBuffer.Protect & 64U) > 0U || (lpBuffer.Protect & 128U) > 0U;
                        bool flag4 = (lpBuffer.Protect & 16U) > 0U || (lpBuffer.Protect & 32U) > 0U || (lpBuffer.Protect & 64U) > 0U || (lpBuffer.Protect & 128U) > 0U;
                        bool flag5 = flag2 & readable;
                        bool flag6 = flag3 & writable;
                        bool flag7 = flag4 & executable;
                        flag1 &= flag5 | flag6 | flag7;
                    }
                    if (!flag1)
                    {
                        lpAddress = new UIntPtr(lpBuffer.BaseAddress.ToUInt64() + (ulong)lpBuffer.RegionSize);
                    }
                    else
                    {
                        MemoryRegionResult memoryRegionResult1 = new MemoryRegionResult()
                        {
                            CurrentBaseAddress = lpAddress,
                            RegionSize = lpBuffer.RegionSize,
                            RegionBase = lpBuffer.BaseAddress
                        };
                        lpAddress = new UIntPtr(lpBuffer.BaseAddress.ToUInt64() + (ulong)lpBuffer.RegionSize);
                        if (source.Count > 0)
                        {
                            MemoryRegionResult memoryRegionResult2 = source[source.Count - 1];
                            if ((long)(ulong)memoryRegionResult2.RegionBase + memoryRegionResult2.RegionSize == (long)(ulong)lpBuffer.BaseAddress)
                            {
                                source[source.Count - 1] = new MemoryRegionResult()
                                {
                                    CurrentBaseAddress = memoryRegionResult2.CurrentBaseAddress,
                                    RegionBase = memoryRegionResult2.RegionBase,
                                    RegionSize = memoryRegionResult2.RegionSize + lpBuffer.RegionSize
                                };
                                continue;
                            }
                        }
                        source.Add(memoryRegionResult1);
                    }
                }
                ConcurrentBag<long> bagResult = new ConcurrentBag<long>();
                Parallel.ForEach<MemoryRegionResult>((IEnumerable<MemoryRegionResult>)source, (Action<MemoryRegionResult, ParallelLoopState, long>)((item, parallelLoopState, index) =>
                {
                    foreach (long num in this.CompareScan(item, aobPattern, mask))
                        bagResult.Add(num);
                }));
                Debug.WriteLine("[DEBUG] memory scan completed. (time:" + DateTime.Now.ToString("h:mm:ss tt") + ")");
                return bagResult.ToList<long>().OrderBy<long, long>((Func<long, long>)(c => c)).AsEnumerable<long>();
            }));
        }

        public string MSize() => !this.Is64Bit ? "x8" : "x16";

        public void CloseProcess()
        {
            IntPtr pHandle = this.pHandle;
            if (false)
                return;
            pain3xmem.CloseHandle(this.pHandle);
            this.theProc = (Process)null;
        }

        public bool Is64Bit
        {
            get => this._is64Bit;
            private set => this._is64Bit = value;
        }

        private unsafe long[] CompareScan(MemoryRegionResult item, byte[] aobPattern, byte[] mask)
        {
            if (mask.Length != aobPattern.Length)
                throw new ArgumentException("aobPattern.Length != mask.Length");
            IntPtr num1 = Marshal.AllocHGlobal((int)item.RegionSize);
            ulong lpNumberOfBytesRead;
            pain3xmem.ReadProcessMemory(this.pHandle, item.CurrentBaseAddress, num1, (UIntPtr)(ulong)item.RegionSize, out lpNumberOfBytesRead);
            int num2 = -aobPattern.Length;
            List<long> longList = new List<long>();
            do
            {
                num2 = this.FindPattern((byte*)num1.ToPointer(), (int)lpNumberOfBytesRead, aobPattern, mask, num2 + aobPattern.Length);
                if (num2 >= 0)
                    longList.Add((long)(ulong)item.CurrentBaseAddress + (long)num2);
            }
            while (num2 != -1);
            Marshal.FreeHGlobal(num1);
            return longList.ToArray();
        }

        private unsafe int FindPattern(
          byte* body,
          int bodyLength,
          byte[] pattern,
          byte[] masks,
          int start = 0)
        {
            int num = -1;
            int pattern1;
            if (bodyLength <= 0 || pattern.Length == 0 || start > bodyLength - pattern.Length || pattern.Length > bodyLength)
            {
                pattern1 = num;
            }
            else
            {
                for (int index1 = start; index1 <= bodyLength - pattern.Length; ++index1)
                {
                    if (((int)body[index1] & (int)masks[0]) == ((int)pattern[0] & (int)masks[0]))
                    {
                        bool flag = true;
                        for (int index2 = 1; index2 <= pattern.Length - 1; ++index2)
                        {
                            if (((int)body[index1 + index2] & (int)masks[index2]) != ((int)pattern[index2] & (int)masks[index2]))
                            {
                                flag = false;
                                break;
                            }
                        }
                        if (flag)
                        {
                            num = index1;
                            break;
                        }
                    }
                }
                pattern1 = num;
            }
            return pattern1;
        }

        public UIntPtr VirtualQueryEx(
          IntPtr hProcess,
          UIntPtr lpAddress,
          out pain3xmem.MEMORY_BASIC_INFORMATION lpBuffer)
        {
            UIntPtr num1;
            if (this.Is64Bit || IntPtr.Size == 8)
            {
                pain3xmem.MEMORY_BASIC_INFORMATION64 lpBuffer1 = new pain3xmem.MEMORY_BASIC_INFORMATION64();
                UIntPtr num2 = pain3xmem.Native_VirtualQueryEx(hProcess, lpAddress, out lpBuffer1, new UIntPtr((uint)Marshal.SizeOf<pain3xmem.MEMORY_BASIC_INFORMATION64>(lpBuffer1)));
                lpBuffer.BaseAddress = lpBuffer1.BaseAddress;
                lpBuffer.AllocationBase = lpBuffer1.AllocationBase;
                lpBuffer.AllocationProtect = lpBuffer1.AllocationProtect;
                lpBuffer.RegionSize = (long)lpBuffer1.RegionSize;
                lpBuffer.State = lpBuffer1.State;
                lpBuffer.Protect = lpBuffer1.Protect;
                lpBuffer.Type = lpBuffer1.Type;
                num1 = num2;
            }
            else
            {
                pain3xmem.MEMORY_BASIC_INFORMATION32 lpBuffer2 = new pain3xmem.MEMORY_BASIC_INFORMATION32();
                UIntPtr num3 = pain3xmem.Native_VirtualQueryEx(hProcess, lpAddress, out lpBuffer2, new UIntPtr((uint)Marshal.SizeOf<pain3xmem.MEMORY_BASIC_INFORMATION32>(lpBuffer2)));
                lpBuffer.BaseAddress = lpBuffer2.BaseAddress;
                lpBuffer.AllocationBase = lpBuffer2.AllocationBase;
                lpBuffer.AllocationProtect = lpBuffer2.AllocationProtect;
                lpBuffer.RegionSize = (long)lpBuffer2.RegionSize;
                lpBuffer.State = lpBuffer2.State;
                lpBuffer.Protect = lpBuffer2.Protect;
                lpBuffer.Type = lpBuffer2.Type;
                num1 = num3;
            }
            return num1;
        }

        public static void notify(string message)
        {
            Process.Start(new ProcessStartInfo("cmd.exe", "/c start cmd /C \"color b && title Error && echo " + message + " && timeout /t 5\"")
            {
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false
            });
            Environment.Exit(0);
        }

        public UIntPtr Get64BitCode(string name, string path = "", int size = 16)
        {
            string str1 = !(path != "") ? name : this.LoadCode(name, path);
            UIntPtr num1;
            if (str1 == "")
            {
                num1 = UIntPtr.Zero;
            }
            else
            {
                if (str1.Contains(" "))
                    str1.Replace(" ", string.Empty);
                string source = str1;
                if (str1.Contains("+"))
                    source = str1.Substring(str1.IndexOf('+') + 1);
                byte[] lpBuffer = new byte[size];
                if (!str1.Contains("+") && !str1.Contains(","))
                    num1 = new UIntPtr(Convert.ToUInt64(str1, 16));
                else if (source.Contains<char>(','))
                {
                    List<long> longList = new List<long>();
                    string str2 = source;
                    char[] chArray = new char[1] { ',' };
                    foreach (string str3 in str2.Split(chArray))
                    {
                        string s = str3;
                        if (str3.Contains("0x"))
                            s = str3.Replace("0x", "");
                        long num2 = str3.Contains("-") ? long.Parse(s.Replace("-", ""), NumberStyles.AllowHexSpecifier) * -1L : long.Parse(s, NumberStyles.AllowHexSpecifier);
                        longList.Add(num2);
                    }
                    long[] array = longList.ToArray();
                    if (str1.Contains("base") || str1.Contains("main"))
                        pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)((long)this.mainModule.BaseAddress + array[0]), lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                    else if (!str1.Contains("base") && !str1.Contains("main") && str1.Contains("+"))
                    {
                        string[] strArray = str1.Split('+');
                        IntPtr num3 = IntPtr.Zero;
                        if (!strArray[0].ToLower().Contains(".dll") && !strArray[0].ToLower().Contains(".exe") && !strArray[0].ToLower().Contains(".bin"))
                        {
                            num3 = (IntPtr)long.Parse(strArray[0], NumberStyles.HexNumber);
                        }
                        else
                        {
                            try
                            {
                                num3 = this.modules[strArray[0]];
                            }
                            catch
                            {
                                Debug.WriteLine("Module " + strArray[0] + " was not found in module list!");
                                Debug.WriteLine("Modules: " + string.Join<KeyValuePair<string, IntPtr>>(",", (IEnumerable<KeyValuePair<string, IntPtr>>)this.modules));
                            }
                        }
                        pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)((long)num3 + array[0]), lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                    }
                    else
                        pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)array[0], lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                    long int64 = BitConverter.ToInt64(lpBuffer, 0);
                    UIntPtr lpBaseAddress = (UIntPtr)0UL;
                    for (int index = 1; index < array.Length; ++index)
                    {
                        lpBaseAddress = new UIntPtr(Convert.ToUInt64(int64 + array[index]));
                        pain3xmem.ReadProcessMemory(this.pHandle, lpBaseAddress, lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                        int64 = BitConverter.ToInt64(lpBuffer, 0);
                    }
                    num1 = lpBaseAddress;
                }
                else
                {
                    long int64 = Convert.ToInt64(source, 16);
                    IntPtr num4 = IntPtr.Zero;
                    if (str1.Contains("base") || str1.Contains("main"))
                        num4 = this.mainModule.BaseAddress;
                    else if (!str1.Contains("base") && !str1.Contains("main") && str1.Contains("+"))
                    {
                        string[] strArray = str1.Split('+');
                        if (!strArray[0].ToLower().Contains(".dll") && !strArray[0].ToLower().Contains(".exe") && !strArray[0].ToLower().Contains(".bin"))
                        {
                            string s = strArray[0];
                            if (s.Contains("0x"))
                                s = s.Replace("0x", "");
                            num4 = (IntPtr)long.Parse(s, NumberStyles.HexNumber);
                        }
                        else
                        {
                            try
                            {
                                num4 = this.modules[strArray[0]];
                            }
                            catch
                            {
                                Debug.WriteLine("Module " + strArray[0] + " was not found in module list!");
                                Debug.WriteLine("Modules: " + string.Join<KeyValuePair<string, IntPtr>>(",", (IEnumerable<KeyValuePair<string, IntPtr>>)this.modules));
                            }
                        }
                    }
                    else
                        num4 = this.modules[str1.Split('+')[0]];
                    num1 = (UIntPtr)(ulong)((long)num4 + int64);
                }
            }
            return num1;
        }

        public UIntPtr GetCode(string name, string path = "", int size = 8)
        {
            UIntPtr code;
            if (this.Is64Bit)
            {
                if (size == 8)
                    size = 16;
                code = this.Get64BitCode(name, path, size);
            }
            else
            {
                string str1 = !(path != "") ? name : this.LoadCode(name, path);
                if (str1 == "")
                {
                    code = UIntPtr.Zero;
                }
                else
                {
                    if (str1.Contains(" "))
                        str1.Replace(" ", string.Empty);
                    if (!str1.Contains("+") && !str1.Contains(","))
                    {
                        code = new UIntPtr(Convert.ToUInt32(str1, 16));
                    }
                    else
                    {
                        string source = str1;
                        if (str1.Contains("+"))
                            source = str1.Substring(str1.IndexOf('+') + 1);
                        byte[] lpBuffer = new byte[size];
                        if (source.Contains<char>(','))
                        {
                            List<int> intList = new List<int>();
                            string str2 = source;
                            char[] chArray = new char[1] { ',' };
                            foreach (string str3 in str2.Split(chArray))
                            {
                                string s = str3;
                                if (str3.Contains("0x"))
                                    s = str3.Replace("0x", "");
                                int num = str3.Contains("-") ? int.Parse(s.Replace("-", ""), NumberStyles.AllowHexSpecifier) * -1 : int.Parse(s, NumberStyles.AllowHexSpecifier);
                                intList.Add(num);
                            }
                            int[] array = intList.ToArray();
                            if (str1.Contains("base") || str1.Contains("main"))
                                pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)((int)this.mainModule.BaseAddress + array[0]), lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                            else if (!str1.Contains("base") && !str1.Contains("main") && str1.Contains("+"))
                            {
                                string[] strArray = str1.Split('+');
                                IntPtr num = IntPtr.Zero;
                                if (!strArray[0].ToLower().Contains(".dll") && !strArray[0].ToLower().Contains(".exe") && !strArray[0].ToLower().Contains(".bin"))
                                {
                                    string s = strArray[0];
                                    if (s.Contains("0x"))
                                        s = s.Replace("0x", "");
                                    num = (IntPtr)int.Parse(s, NumberStyles.HexNumber);
                                }
                                else
                                {
                                    try
                                    {
                                        num = this.modules[strArray[0]];
                                    }
                                    catch
                                    {
                                        Debug.WriteLine("Module " + strArray[0] + " was not found in module list!");
                                        Debug.WriteLine("Modules: " + string.Join<KeyValuePair<string, IntPtr>>(",", (IEnumerable<KeyValuePair<string, IntPtr>>)this.modules));
                                    }
                                }
                                pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)((int)num + array[0]), lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                            }
                            else
                                pain3xmem.ReadProcessMemory(this.pHandle, (UIntPtr)(ulong)array[0], lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                            uint uint32 = BitConverter.ToUInt32(lpBuffer, 0);
                            UIntPtr lpBaseAddress = (UIntPtr)0UL;
                            for (int index = 1; index < array.Length; ++index)
                            {
                                lpBaseAddress = new UIntPtr(Convert.ToUInt32((long)uint32 + (long)array[index]));
                                pain3xmem.ReadProcessMemory(this.pHandle, lpBaseAddress, lpBuffer, (UIntPtr)(ulong)size, IntPtr.Zero);
                                uint32 = BitConverter.ToUInt32(lpBuffer, 0);
                            }
                            code = lpBaseAddress;
                        }
                        else
                        {
                            int int32 = Convert.ToInt32(source, 16);
                            IntPtr num = IntPtr.Zero;
                            if (str1.ToLower().Contains("base") || str1.ToLower().Contains("main"))
                                num = this.mainModule.BaseAddress;
                            else if (!str1.ToLower().Contains("base") && !str1.ToLower().Contains("main") && str1.Contains("+"))
                            {
                                string[] strArray = str1.Split('+');
                                if (!strArray[0].ToLower().Contains(".dll") && !strArray[0].ToLower().Contains(".exe") && !strArray[0].ToLower().Contains(".bin"))
                                {
                                    string s = strArray[0];
                                    if (s.Contains("0x"))
                                        s = s.Replace("0x", "");
                                    num = (IntPtr)int.Parse(s, NumberStyles.HexNumber);
                                }
                                else
                                {
                                    try
                                    {
                                        num = this.modules[strArray[0]];
                                    }
                                    catch
                                    {
                                        Debug.WriteLine("Module " + strArray[0] + " was not found in module list!");
                                        Debug.WriteLine("Modules: " + string.Join<KeyValuePair<string, IntPtr>>(",", (IEnumerable<KeyValuePair<string, IntPtr>>)this.modules));
                                    }
                                }
                            }
                            else
                                num = this.modules[str1.Split('+')[0]];
                            code = (UIntPtr)(ulong)((int)num + int32);
                        }
                    }
                }
            }
            return code;
        }

        public bool WriteMemory(
          string code,
          string type,
          string write,
          string file = "",
          Encoding stringEncoding = null)
        {
            byte[] lpBuffer = new byte[4];
            int nSize = 4;
            UIntPtr code1 = this.GetCode(code, file);
            if (type.ToLower() == "float")
            {
                lpBuffer = BitConverter.GetBytes(Convert.ToSingle(write));
                nSize = 4;
            }
            else if (type.ToLower() == "int")
            {
                lpBuffer = BitConverter.GetBytes(Convert.ToInt32(write));
                nSize = 4;
            }
            else if (type.ToLower() == "byte")
            {
                lpBuffer = new byte[1] { Convert.ToByte(write, 16) };
                nSize = 1;
            }
            else if (type.ToLower() == "2bytes")
            {
                lpBuffer = new byte[2]
                {
          (byte) (Convert.ToInt32(write) % 256),
          (byte) (Convert.ToInt32(write) / 256)
                };
                nSize = 2;
            }
            else if (type.ToLower() == "bytes")
            {
                if (write.Contains(",") || write.Contains(" "))
                {
                    string[] source;
                    if (write.Contains(","))
                        source = write.Split(',');
                    else
                        source = write.Split(' ');
                    int length = ((IEnumerable<string>)source).Count<string>();
                    lpBuffer = new byte[length];
                    for (int index = 0; index < length; ++index)
                        lpBuffer[index] = Convert.ToByte(source[index], 16);
                    nSize = ((IEnumerable<string>)source).Count<string>();
                }
                else
                {
                    lpBuffer = new byte[1]
                    {
            Convert.ToByte(write, 16)
                    };
                    nSize = 1;
                }
            }
            else if (type.ToLower() == "double")
            {
                lpBuffer = BitConverter.GetBytes(Convert.ToDouble(write));
                nSize = 8;
            }
            else if (type.ToLower() == "long")
            {
                lpBuffer = BitConverter.GetBytes(Convert.ToInt64(write));
                nSize = 8;
            }
            else if (type.ToLower() == "string")
            {
                lpBuffer = stringEncoding != null ? stringEncoding.GetBytes(write) : Encoding.UTF8.GetBytes(write);
                nSize = lpBuffer.Length;
            }
            return pain3xmem.WriteProcessMemory(this.pHandle, code1, lpBuffer, (UIntPtr)(ulong)nSize, IntPtr.Zero);
        }

        public bool IsAdmin()
        {
            using (WindowsIdentity current = WindowsIdentity.GetCurrent())
                return new WindowsPrincipal(current).IsInRole(WindowsBuiltInRole.Administrator);
        }

        public bool OpenProcess(int pid)
        {
            if (!this.IsAdmin())
            {
                Debug.WriteLine("WARNING: You are NOT running this program as admin! Visit https://discord.gg/emmWnPRrYX");
                pain3xmem.notify("WARNING: You are NOT running this program as admin! For More Help Visit https://discord.gg/emmWnPRrYX");
            }
            bool flag;
            if (pid <= 0)
            {
                Debug.WriteLine("ERROR: OpenProcess given proc ID 0.");
                flag = false;
            }
            else if (this.theProc != null && this.theProc.Id == pid)
            {
                flag = true;
            }
            else
            {
                try
                {
                    this.theProc = Process.GetProcessById(pid);
                    if (this.theProc != null && !this.theProc.Responding)
                    {
                        Debug.WriteLine("ERROR: OpenProcess: Process is not responding or null.");
                        flag = false;
                    }
                    else
                    {
                        this.pHandle = pain3xmem.OpenProcess(2035711U, true, pid);
                        Process.EnterDebugMode();
                        if (this.pHandle == IntPtr.Zero)
                        {
                            Debug.WriteLine("ERROR: OpenProcess has failed opening a handle to the target process (GetLastWin32ErrorCode: " + Marshal.GetLastWin32Error().ToString() + ")");
                            Process.LeaveDebugMode();
                            this.theProc = (Process)null;
                            flag = false;
                        }
                        else
                        {
                            this.mainModule = this.theProc.MainModule;
                            this.GetModules();
                            bool lpSystemInfo;
                            this.Is64Bit = Environment.Is64BitOperatingSystem && pain3xmem.IsWow64Process(this.pHandle, out lpSystemInfo) && !lpSystemInfo;
                            Debug.WriteLine("Program is operating at Administrative level. Process #" + this.theProc?.ToString() + " is open and modules are stored.");
                            flag = true;
                        }
                    }
                }
                catch
                {
                    flag = false;
                }
            }
            return flag;
        }

        public void GetModules()
        {
            if (this.theProc == null)
                return;
            this.modules.Clear();
            foreach (ProcessModule module in (ReadOnlyCollectionBase)this.theProc.Modules)
            {
                if (!string.IsNullOrEmpty(module.ModuleName) && !this.modules.ContainsKey(module.ModuleName))
                    this.modules.Add(module.ModuleName, module.BaseAddress);
            }
        }

        public Task<IEnumerable<long>> AoBScan2(
          string search,
          bool writable = false,
          bool executable = false,
          string file = "")
        {
            return this.AoBScan(0L, long.MaxValue, search, writable, executable, file);
        }

        public Task<IEnumerable<long>> AoBScan(
          long start,
          long end,
          string search,
          bool writable,
          bool executable,
          string file = "")
        {
            return this.AoBScan(start, end, search, true, writable, executable, file);
        }

        public bool ChangeProtection(
          string code,
          pain3xmem.MemoryProtection newProtection,
          out pain3xmem.MemoryProtection oldProtection,
          string file = "")
        {
            UIntPtr code1 = this.GetCode(code, file);
            bool flag;
            if (code1 == UIntPtr.Zero || this.pHandle == IntPtr.Zero)
            {
                oldProtection = (pain3xmem.MemoryProtection)0;
                flag = false;
            }
            else
                flag = pain3xmem.VirtualProtectEx(this.pHandle, code1, (IntPtr)(this.Is64Bit ? 8 : 4), newProtection, out oldProtection);
            return flag;
        }

        [Flags]
        public enum ThreadAccess
        {
            TERMINATE = 1,
            SUSPEND_RESUME = 2,
            GET_CONTEXT = 8,
            SET_CONTEXT = 16, // 0x00000010
            SET_INFORMATION = 32, // 0x00000020
            QUERY_INFORMATION = 64, // 0x00000040
            SET_THREAD_TOKEN = 128, // 0x00000080
            IMPERSONATE = 256, // 0x00000100
            DIRECT_IMPERSONATION = 512, // 0x00000200
        }

        public struct MEMORY_BASIC_INFORMATION32
        {
            public UIntPtr BaseAddress;
            public UIntPtr AllocationBase;
            public uint AllocationProtect;
            public uint RegionSize;
            public uint State;
            public uint Protect;
            public uint Type;
        }

        public struct MEMORY_BASIC_INFORMATION64
        {
            public UIntPtr BaseAddress;
            public UIntPtr AllocationBase;
            public uint AllocationProtect;
            public uint __alignment1;
            public ulong RegionSize;
            public uint State;
            public uint Protect;
            public uint Type;
            public uint __alignment2;
        }

        [Flags]
        public enum MemoryProtection : uint
        {
            Execute = 16, // 0x00000010
            ExecuteRead = 32, // 0x00000020
            ExecuteReadWrite = 64, // 0x00000040
            ExecuteWriteCopy = 128, // 0x00000080
            NoAccess = 1,
            ReadOnly = 2,
            ReadWrite = 4,
            WriteCopy = 8,
            GuardModifierflag = 256, // 0x00000100
            NoCacheModifierflag = 512, // 0x00000200
            WriteCombineModifierflag = 1024, // 0x00000400
        }

        public struct SYSTEM_INFO
        {
            public ushort processorArchitecture;
            private ushort reserved;
            public uint pageSize;
            public UIntPtr minimumApplicationAddress;
            public UIntPtr maximumApplicationAddress;
            public IntPtr activeProcessorMask;
            public uint numberOfProcessors;
            public uint processorType;
            public uint allocationGranularity;
            public ushort processorLevel;
            public ushort processorRevision;
        }

        public struct MEMORY_BASIC_INFORMATION
        {
            public UIntPtr BaseAddress;
            public UIntPtr AllocationBase;
            public uint AllocationProtect;
            public long RegionSize;
            public uint State;
            public uint Protect;
            public uint Type;
        }
    }
}
