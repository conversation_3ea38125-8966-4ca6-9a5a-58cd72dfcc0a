using System;
using System.Drawing;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AotForms
{
    /// <summary>
    /// UI performance optimizations and responsiveness improvements
    /// </summary>
    internal static class UIOptimizations
    {
        private static System.Threading.Timer _uiUpdateTimer;
        private static volatile bool _isOptimized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Apply UI optimizations
        /// </summary>
        public static void ApplyUIOptimizations(Form mainForm)
        {
            lock (_lockObject)
            {
                if (_isOptimized) return;

                try
                {
                    // Enable double buffering for smoother rendering
                    EnableDoubleBuffering(mainForm);

                    // Optimize form properties
                    OptimizeFormProperties(mainForm);

                    // Start UI update optimization
                    StartUIUpdateOptimization();

                    _isOptimized = true;
                    ErrorHandler.LogInfo("UI optimizations applied successfully");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Failed to apply UI optimizations", ex);
                }
            }
        }

        /// <summary>
        /// Set control style using reflection
        /// </summary>
        private static void SetControlStyle(Control control, ControlStyles style, bool value)
        {
            try
            {
                var setStyleMethod = typeof(Control).GetMethod("SetStyle",
                    BindingFlags.Instance | BindingFlags.NonPublic);
                setStyleMethod?.Invoke(control, new object[] { style, value });
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError($"Error setting control style {style}", ex);
            }
        }

        /// <summary>
        /// Enable double buffering for smoother rendering
        /// </summary>
        private static void EnableDoubleBuffering(Control control)
        {
            try
            {
                if (control is Form form)
                {
                    // Enable double buffering using reflection
                    var doubleBufferPropertyInfo = form.GetType().GetProperty("DoubleBuffered",
                        System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

                    doubleBufferPropertyInfo?.SetValue(form, true, null);

                    // Set additional rendering optimizations using reflection
                    SetControlStyle(form, ControlStyles.AllPaintingInWmPaint, true);
                    SetControlStyle(form, ControlStyles.UserPaint, true);
                    SetControlStyle(form, ControlStyles.DoubleBuffer, true);
                    SetControlStyle(form, ControlStyles.ResizeRedraw, true);
                    SetControlStyle(form, ControlStyles.OptimizedDoubleBuffer, true);
                }

                // Apply to all child controls
                foreach (Control child in control.Controls)
                {
                    EnableDoubleBuffering(child);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error enabling double buffering", ex);
            }
        }

        /// <summary>
        /// Optimize form properties for better performance
        /// </summary>
        private static void OptimizeFormProperties(Form form)
        {
            try
            {
                // Suspend layout during optimization
                form.SuspendLayout();

                // Optimize rendering
                form.AutoScaleMode = AutoScaleMode.None; // Disable auto-scaling for performance
                form.UseWaitCursor = false;

                // Optimize updates
                SetControlStyle(form, ControlStyles.EnableNotifyMessage, false);
                SetControlStyle(form, ControlStyles.FixedWidth, false);
                SetControlStyle(form, ControlStyles.FixedHeight, false);

                // Resume layout
                form.ResumeLayout(false);

                ErrorHandler.LogInfo("Form properties optimized");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error optimizing form properties", ex);
            }
        }

        /// <summary>
        /// Start UI update optimization
        /// </summary>
        private static void StartUIUpdateOptimization()
        {
            try
            {
                // Limit UI updates to reasonable frequency
                _uiUpdateTimer = new System.Threading.Timer(OptimizeUIUpdates, null,
                    TimeSpan.FromMilliseconds(100), TimeSpan.FromMilliseconds(100));

                ErrorHandler.LogInfo("UI update optimization started");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to start UI update optimization", ex);
            }
        }

        /// <summary>
        /// Optimize UI updates
        /// </summary>
        private static void OptimizeUIUpdates(object state)
        {
            try
            {
                // Force UI thread to process pending messages efficiently
                Application.DoEvents();

                // Trigger garbage collection for UI objects if needed
                if (GC.GetTotalMemory(false) > PerformanceConfig.MemoryPressureThresholdMB * 1024 * 1024)
                {
                    GC.Collect(0, GCCollectionMode.Optimized); // Only collect generation 0
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("UI update optimization error", ex);
            }
        }

        /// <summary>
        /// Optimize control rendering
        /// </summary>
        public static void OptimizeControlRendering(Control control)
        {
            try
            {
                if (control == null) return;

                control.SuspendLayout();

                // Optimize painting
                SetControlStyle(control, ControlStyles.AllPaintingInWmPaint, true);
                SetControlStyle(control, ControlStyles.UserPaint, true);
                SetControlStyle(control, ControlStyles.DoubleBuffer, true);
                SetControlStyle(control, ControlStyles.OptimizedDoubleBuffer, true);
                SetControlStyle(control, ControlStyles.ResizeRedraw, false); // Only redraw when necessary

                // Optimize updates
                SetControlStyle(control, ControlStyles.EnableNotifyMessage, false);
                SetControlStyle(control, ControlStyles.SupportsTransparentBackColor, false);

                control.ResumeLayout(false);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Control rendering optimization error", ex);
            }
        }

        /// <summary>
        /// Batch UI updates for better performance
        /// </summary>
        public static async Task BatchUIUpdates(Control control, Action updateAction)
        {
            if (control == null || updateAction == null) return;

            try
            {
                if (control.InvokeRequired)
                {
                    await Task.Run(() =>
                    {
                        control.Invoke(new Action(() =>
                        {
                            control.SuspendLayout();
                            try
                            {
                                updateAction();
                            }
                            finally
                            {
                                control.ResumeLayout(true);
                            }
                        }));
                    });
                }
                else
                {
                    control.SuspendLayout();
                    try
                    {
                        updateAction();
                    }
                    finally
                    {
                        control.ResumeLayout(true);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Batch UI update error", ex);
            }
        }

        /// <summary>
        /// Optimize timer-based UI updates
        /// </summary>
        public static void OptimizeTimerUpdates(System.Windows.Forms.Timer timer, int optimalInterval = 50)
        {
            try
            {
                if (timer == null) return;

                // Set optimal interval (20 FPS for UI updates)
                timer.Interval = Math.Max(optimalInterval, 16); // Minimum 60 FPS

                ErrorHandler.LogInfo($"Timer optimized: Interval set to {timer.Interval}ms");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Timer optimization error", ex);
            }
        }

        /// <summary>
        /// Reduce UI flicker
        /// </summary>
        public static void ReduceFlicker(Control control)
        {
            try
            {
                if (control == null) return;

                // Enable double buffering and optimize painting
                SetControlStyle(control, ControlStyles.AllPaintingInWmPaint, true);
                SetControlStyle(control, ControlStyles.UserPaint, true);
                SetControlStyle(control, ControlStyles.DoubleBuffer, true);
                SetControlStyle(control, ControlStyles.OptimizedDoubleBuffer, true);

                // Reduce unnecessary redraws
                SetControlStyle(control, ControlStyles.ResizeRedraw, false);
                SetControlStyle(control, ControlStyles.Opaque, true);
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Flicker reduction error", ex);
            }
        }

        /// <summary>
        /// Optimize font rendering
        /// </summary>
        public static void OptimizeFontRendering(Control control)
        {
            try
            {
                if (control?.Font == null) return;

                // Use optimized font settings
                var optimizedFont = new Font(control.Font.FontFamily, control.Font.Size,
                    control.Font.Style, GraphicsUnit.Pixel);

                control.Font = optimizedFont;

                // Apply to child controls
                foreach (Control child in control.Controls)
                {
                    OptimizeFontRendering(child);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Font rendering optimization error", ex);
            }
        }

        /// <summary>
        /// Cleanup UI optimizations
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                _uiUpdateTimer?.Dispose();
                _uiUpdateTimer = null;
                _isOptimized = false;

                ErrorHandler.LogInfo("UI optimizations cleaned up");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("UI optimization cleanup error", ex);
            }
        }

        /// <summary>
        /// Check if UI optimizations are active
        /// </summary>
        public static bool IsOptimized => _isOptimized;
    }

    /// <summary>
    /// Responsive UI helper for better user experience
    /// </summary>
    internal static class ResponsiveUI
    {
        /// <summary>
        /// Make UI updates responsive by running them on background thread
        /// </summary>
        public static async Task UpdateUIAsync(Control control, Action updateAction)
        {
            if (control == null || updateAction == null) return;

            try
            {
                await Task.Run(() =>
                {
                    if (control.IsDisposed) return;

                    if (control.InvokeRequired)
                    {
                        control.BeginInvoke(updateAction);
                    }
                    else
                    {
                        updateAction();
                    }
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Responsive UI update error", ex);
            }
        }

        /// <summary>
        /// Throttle UI updates to prevent overwhelming the UI thread
        /// </summary>
        public static void ThrottleUIUpdates(Control control, Action updateAction, int throttleMs = 100)
        {
            var lastUpdate = DateTime.MinValue;

            if (DateTime.Now - lastUpdate < TimeSpan.FromMilliseconds(throttleMs))
                return;

            lastUpdate = DateTime.Now;
            _ = UpdateUIAsync(control, updateAction);
        }
    }
}
