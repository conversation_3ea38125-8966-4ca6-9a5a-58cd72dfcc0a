﻿
using System.Runtime.InteropServices;
using System.Text;
using System.Collections.Concurrent;

namespace AotForms
{
    internal static class InternalMemory
    {
        [DllImport("AotBst.dll")]
        static extern nint CPU(nint pVM, uint cpuId);

        [DllImport("AotBst.dll")]
        static extern int InternalRead(nint pVM, ulong address, nint buffer, uint size);

        [DllImport("AotBst.dll")]
        static extern int Cast(nint pVCpu, ulong address, out ulong physAddress);

        [DllImport("AotBst.dll")]
        static extern int InternalWrite(nint pVM, ulong address, nint buffer, uint size);

        private static nint pVMAddr;
        private static nint cpuAddr;
        private static readonly object _lockObject = new object();
        private static volatile bool _isInitialized = false;

        // Use ConcurrentDictionary for thread-safe cache operations
        internal static ConcurrentDictionary<ulong, ulong> Cache = new();

        internal static void Initialize(nint pVM)
        {
            lock (_lockObject)
            {
                try
                {
                    pVMAddr = pVM;
                    cpuAddr = CPU(pVM, 0);
                    Cache = new ConcurrentDictionary<ulong, ulong>();
                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("InternalMemory initialization error", ex);
                    _isInitialized = false;
                }
            }
        }

        internal static bool Convert(ulong address, out ulong phys)
        {
            phys = 0;

            if (!_isInitialized)
                return false;

            try
            {
                // Check cache first
                if (Cache.TryGetValue(address, out var cachedPhys))
                {
                    phys = cachedPhys;
                    return true;
                }

                // Get CPU address safely
                lock (_lockObject)
                {
                    cpuAddr = CPU(pVMAddr, 0);
                }

                var status = Cast(cpuAddr, address, out phys);

                if (status == 0 && !Config.NoCache && PerformanceConfig.EnableCache)
                {
                    // Add to cache with size limit
                    if (Cache.Count < PerformanceConfig.MaxCacheSize)
                    {
                        Cache.TryAdd(address, phys);
                    }
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Address conversion error", ex);
                return false;
            }
        }

        internal static unsafe bool Read<T>(ulong address, out T data) where T : struct
        {
            data = default;

            if (!_isInitialized)
                return false;

            try
            {
                if (!Convert(address, out address))
                    return false;

                T buffer = default;
                var bufferReference = __makeref(buffer);
                var size = (uint)Marshal.SizeOf<T>();

                var status = InternalRead(pVMAddr, address, *(nint*)&bufferReference, size);
                data = buffer;
                return status == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Read error: {ex.Message}");
                return false;
            }
        }

        internal static unsafe bool ReadArray<T>(ulong address, ref T[] array) where T : struct
        {
            if (!_isInitialized || array == null || array.Length == 0)
                return false;

            try
            {
                if (!Convert(address, out address))
                    return false;

                var size = (uint)((ulong)Marshal.SizeOf(array[0]) * (ulong)array.Length);
                var typedReference = __makeref(array[0]);

                var status = InternalRead(pVMAddr, address, *(nint*)&typedReference, size);
                return status == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReadArray error: {ex.Message}");
                return false;
            }
        }

        internal static string ReadString(ulong address, int size, bool unicode = true)
        {
            if (!_isInitialized || size <= 0 || size > PerformanceConfig.MaxStringLength)
                return "";

            try
            {
                var stringBytes = new byte[size];
                var read = ReadArray(address, ref stringBytes);

                if (!read) return "";

                var readString = unicode ? Encoding.Unicode.GetString(stringBytes) : Encoding.Default.GetString(stringBytes);

                if (readString.Contains('\0'))
                    readString = readString.Substring(0, readString.IndexOf('\0'));

                return readString;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReadString error: {ex.Message}");
                return "";
            }
        }

        internal static unsafe void Write<T>(ulong address, T value) where T : struct
        {
            if (!_isInitialized)
                return;

            try
            {
                if (!Convert(address, out address))
                    return;

                var size = (uint)Marshal.SizeOf<T>();
                var bufferReference = __makeref(value);

                InternalWrite(pVMAddr, address, *(nint*)&bufferReference, size);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Write error: {ex.Message}");
            }
        }

        // Cache management methods
        internal static void ClearCache()
        {
            try
            {
                Cache.Clear();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Cache clear error: {ex.Message}");
            }
        }

        internal static int GetCacheSize()
        {
            return Cache.Count;
        }

        internal static bool IsInitialized => _isInitialized;
    }
}
