﻿using AotForms;
using System.Numerics;

internal static class UpPlayer
{
    private static Task upTask;
    private static CancellationTokenSource cts = new();
    private static bool isRunning = false;
    public static float DownSpeed = 0.04f;
    public static float MinYLimit = -10f;

    internal static void Work()
    {
        if (isRunning) return;
        isRunning = true;

        upTask = Task.Run(async () =>
        {
            while (!cts.Token.IsCancellationRequested)
            {
                if (!Config.UpPlayer)
                {
                    await Task.Delay(1, cts.Token);
                    continue;
                }

                if (Core.Width == -1 || Core.Height == -1 || !Core.HaveMatrix)
                {
                    await Task.Delay(1, cts.Token);
                    continue;
                }

                foreach (var entity in Core.Entities.Values)
                {
       
                    if (entity.IsDead || entity.IsKnocked || !entity.IsKnown)
                        continue;

                    try
                    {
                        if (!InternalMemory.Read(entity.Address + (uint)Bones.Root, out uint enemyRootBonePtr)) continue;
                        if (!InternalMemory.Read(enemyRootBonePtr + 0x8, out uint enemyTransformValue)) continue;
                        if (!InternalMemory.Read(enemyTransformValue + 0x8, out uint enemyTransformObjPtr)) continue;
                        if (!InternalMemory.Read(enemyTransformObjPtr + 0x20, out uint enemyMatrixValue)) continue;

                        if (!InternalMemory.Read(enemyMatrixValue + 0x80, out Vector3 enemyPos)) continue;

                      
                        enemyPos.Y -= 0.1f;

                        InternalMemory.Write(enemyMatrixValue + 0x80, enemyPos);
                    }
                    catch
                    {
                        
                    }
                }

                await Task.Delay(1, cts.Token);
            }
        }, cts.Token);
    }
    internal static void Stop()
    {
        if (!isRunning) return;

        cts.Cancel();
        cts = new CancellationTokenSource();
        isRunning = false;
    }
}
