﻿//using System;

//namespace AotForms
//{
//    internal static class PlayerWeaponInfo
//    {
//        // ... existing code ...
//        public static string GetCurrentWeaponName()
//        {
//            // ... existing code ...
//            uint localPlayer = (uint)Core.LocalPlayer;
//            if (localPlayer == 0)
//                return "Nessun Player";

//            // Leggi il puntatore all'arma
//            if (!InternalMemory.Read<uint>(localPlayer + Offsets.Weapon, out var weaponPtr) || weaponPtr == 0)
//                return "Nessuna Arma";

//            // Leggi i dati dell'arma
//            if (!InternalMemory.Read<uint>(weaponPtr + Offsets.WeaponData, out var weaponData) || weaponData == 0)
//                return "Dati Arma Mancanti";

//            // Leggi il nome dell'arma
//            string weaponName = InternalMemory.ReadString(
//                weaponData + Offsets.WeaponName,
//                (int)Offsets.WeaponNameLength * 2,
//                true);

//            return string.IsNullOrEmpty(weaponName) ? "Sconosciuta" : weaponName;
//        }
//        // ... existing code ...
//    }
//}