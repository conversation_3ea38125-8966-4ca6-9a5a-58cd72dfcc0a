﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace AotForms
{
    class Particles1
    {
        private List<Particle> particles;
        private Random random = new Random();
        private Color particleColor = Color.FromArgb(150, 255, 215, 100);

        private int glowSize = 10;
        private System.Windows.Forms.Timer animationTimer;
        private Guna2Panel panel;
        private int particlesCount;

        public bool IsActive { get; private set; }

        // Costruttore modificato per accettare solo 2 parametri
        public Particles1(Guna2Panel targetPanel, Guna2CustomCheckBox toggleCheckBox)
        {
            panel = targetPanel;
            particlesCount = 25; // Valore predefinito
            particles = new List<Particle>();

            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 16;
            animationTimer.Tick += (s, e) => {
                UpdateParticles();
                panel.Invalidate();
            };

            toggleCheckBox.CheckedChanged += (s, e) => {
                IsActive = toggleCheckBox.Checked;
                if (IsActive)
                {
                    InitializeParticles();
                    animationTimer.Start();
                }
                else
                {
                    animationTimer.Stop();
                    particles.Clear();
                    panel.Invalidate();
                }
            };

            typeof(Panel).InvokeMember("DoubleBuffered",
                System.Reflection.BindingFlags.SetProperty |
                System.Reflection.BindingFlags.Instance |
                System.Reflection.BindingFlags.NonPublic,
                null, panel, new object[] { true });

            panel.Paint += (s, e) => {
                if (IsActive)
                    DrawParticles(e.Graphics);
            };
        }

        private void InitializeParticles()
        {
            particles.Clear();
            float centerX = panel.Width / 2f;

            for (int i = 0; i < particlesCount; i++)
            {
                particles.Add(new Particle(
                    new PointF(centerX, -5),
                    random.Next(2, 4),
                    new PointF(0, random.Next(1, 3)),
                    i % 2 == 0
                ));
            }
        }

        private void UpdateParticles()
        {
            float centerX = panel.Width / 2f;
            float panelHeight = panel.Height;
            float panelWidth = panel.Width;

            for (int i = 0; i < particles.Count; i++)
            {
                Particle p = particles[i];

                // Creiamo nuove variabili per modificare i valori
                PointF newPosition = p.Position;
                PointF newSpeed = p.Speed;

                if (!p.HasSplit)
                {
                    // Fase 1: Discesa verticale
                    newPosition.Y += newSpeed.Y;

                    // Divisione appena visibile
                    if (newPosition.Y >= 5f)
                    {
                        p.HasSplit = true;
                        float horizontalSpeed = random.Next(1, 3);
                        newSpeed.X = p.IsLeft ? -horizontalSpeed : horizontalSpeed;
                        newSpeed.Y *= 0.7f;
                    }
                }
                else
                {
                    // Fase 2: Movimento diagonale
                    newPosition.X += newSpeed.X;
                    newPosition.Y += newSpeed.Y;

                    // Riciclo particelle uscite
                    if (newPosition.Y > panelHeight + 10 ||
                        newPosition.X < -15 || newPosition.X > panelWidth + 15)
                    {
                        newPosition = new PointF(centerX, -5);
                        newSpeed = new PointF(0, random.Next(1, 3));
                        p.HasSplit = false;
                    }
                }

                // Aggiorniamo la particella con i nuovi valori
                particles[i] = new Particle(newPosition, p.Size, newSpeed, p.IsLeft, p.HasSplit);
            }
        }

        private void DrawParticles(Graphics g)
        {
            g.SmoothingMode = SmoothingMode.HighQuality;

            foreach (var p in particles)
            {
                // Glow effect
                using (var path = new GraphicsPath())
                {
                    path.AddEllipse(p.Position.X - glowSize / 2, p.Position.Y - glowSize / 2, glowSize, glowSize);
                    using (var brush = new PathGradientBrush(path))
                    {
                        brush.CenterColor = Color.FromArgb(80, particleColor);
                        brush.SurroundColors = new[] { Color.FromArgb(0, particleColor) };
                        g.FillEllipse(brush, p.Position.X - glowSize / 2, p.Position.Y - glowSize / 2, glowSize, glowSize);
                    }
                }

                // Particella
                using (var brush = new SolidBrush(particleColor))
                {
                    g.FillEllipse(brush,
                        p.Position.X - p.Size / 2,
                        p.Position.Y - p.Size / 2,
                        p.Size, p.Size);
                }
            }
        }
    }

    struct Particle
    {
        public PointF Position { get; }
        public float Size { get; }
        public PointF Speed { get; }
        public bool IsLeft { get; }
        public bool HasSplit { get; set; }

        public Particle(PointF position, float size, PointF speed, bool isLeft, bool hasSplit = false)
        {
            Position = position;
            Size = size;
            Speed = speed;
            IsLeft = isLeft;
            HasSplit = hasSplit;
        }
    }
}