﻿// Decompiled with JetBrains decompiler
// Type: CXMem.CX
// Assembly: PAIN3XPREMIUM, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: A4668FF3-823C-4772-950D-300068930571
// Assembly location: C:\Users\<USER>\Downloads\svchost.exe

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using HeXmem;


namespace Client
{

    public class Flame
    {

        public bool isPrivate;
        public int processId;
        public IntPtr _processHandle;
        private bool _enableCheck = true;
        public const uint MEM_COMMIT = 4096;
        public const uint MEM_PRIVATE = 131072;
        public const uint PAGE_READWRITE = 4;
        // Inizializza la protezione all'avvio

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr OpenProcess(
          ProcessAccessFlags dwDesiredAccess,
          [MarshalAs(UnmanagedType.Bool)] bool bInheritHandle,
          int dwProcessId);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr VirtualAllocEx(
          IntPtr hProcess,
          IntPtr lpAddress,
          uint dwSize,
          uint flAllocationType,
          uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern int VirtualQueryEx(
          IntPtr hProcess,
          IntPtr lpAddress,
          out Flame.MEMORY_BASIC_INFORMATION lpBuffer,
          uint dwLength);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool ReadProcessMemory(
          IntPtr hProcess,
          IntPtr lpBaseAddress,
          [Out] byte[] lpBuffer,
          IntPtr nSize,
          out IntPtr lpNumberOfBytesRead);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool WriteProcessMemory(
          IntPtr hProcess,
          IntPtr lpBaseAddress,
          byte[] lpBuffer,
          IntPtr nSize,
          IntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenThread(
          ThreadAccess dwDesiredAccess,
          bool bInheritHandle,
          uint dwThreadId);

        [DllImport("kernel32.dll")]
        public static extern int ResumeThread(IntPtr hThread);

        [DllImport("kernel32.dll")]
        public static extern int CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern uint WaitForSingleObject(IntPtr hHandle, uint dwMilliseconds);

        [DllImport("kernel32.dll")]
        public static extern IntPtr CreateRemoteThread(
          IntPtr hProcess,
          IntPtr lpThreadAttributes,
          uint dwStackSize,
          IntPtr lpStartAddress,
          IntPtr lpParameter,
          uint dwCreationFlags,
          IntPtr lpThreadId);

        public bool SetProcess(string[] processNames)
        {
            this.processId = 0;
            foreach (Process process in Process.GetProcesses())
            {
                string processName = process.ProcessName;
                if (Array.Exists<string>(processNames, (Predicate<string>)(name => name.Equals(processName, StringComparison.CurrentCultureIgnoreCase))))
                {
                    this.processId = process.Id;
                    break;
                }
            }
            if (this.processId <= 0)
                return false;
            this._processHandle = Flame.OpenProcess(ProcessAccessFlags.AllAccess, false, this.processId);
            return !(this._processHandle == IntPtr.Zero);
        }

        public void CheckProcess()
        {
            if (!this._enableCheck)
                return;
            foreach (ProcessThread thread in (ReadOnlyCollectionBase)Process.GetProcessById(this.processId).Threads)
            {
                IntPtr num = Flame.OpenThread(ThreadAccess.SUSPEND_RESUME, false, (uint)thread.Id);
                if (num != IntPtr.Zero)
                {
                    do
                        ;
                    while (Flame.ResumeThread(num) > 0);
                    Flame.CloseHandle(num);
                }
            }
        }

        public async Task<IEnumerable<long>> AoBScan(string bytePattern)
        {
            return await this.AobScan(bytePattern);
        }

        private async Task<IEnumerable<long>> AobScan(string pattern)
        {
            var patternData = GetPatternDataFromPattern(pattern);
            var result = new ConcurrentBag<long>(); // Thread-safe per Parallel.ForEach
            var pages = new List<MemoryPage>();

            await Task.Run(() =>
            {
                var bufferInfo = new MEMORY_BASIC_INFORMATION();
                var ptr = IntPtr.Zero;
                var structSize = (uint)Marshal.SizeOf(typeof(MEMORY_BASIC_INFORMATION));

                while (VirtualQueryEx(_processHandle, ptr, out bufferInfo, structSize) == structSize)
                {
                    if (CanReadPage(bufferInfo) && (ulong)bufferInfo.RegionSize.ToUInt64() >= 1024) // Evita micro-pagine
                        pages.Add(new MemoryPage(ptr, (int)bufferInfo.RegionSize.ToUInt64()));

                    ptr = (IntPtr)((long)bufferInfo.BaseAddress + (long)(ulong)bufferInfo.RegionSize);
                }

                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount
                };

                Parallel.ForEach(pages, parallelOptions, page =>
                {
                    byte[] buffer = new byte[page.Size];
                    if (ReadProcessMemory(_processHandle, page.Start, buffer, (IntPtr)page.Size, out var bytesRead))
                    {
                        int index = -patternData.pattern.Length;
                        do
                        {
                            index = FindPattern(buffer, patternData.pattern, patternData.mask, index + patternData.pattern.Length);
                            if (index >= 0)
                                result.Add((long)page.Start + index);
                        } while (index != -1);
                    }
                });
            });

            return result.OrderBy(c => c).ToArray();
        }


        public bool CanReadPage(Flame.MEMORY_BASIC_INFORMATION page)
        {
            return page.State == 4096U && page.Type == 131072U && page.Protect == 4U;
        }

        private Flame.PatternData GetPatternDataFromPattern(string pattern)
        {
            string[] source = pattern.Split(' ');
            return new Flame.PatternData()
            {
                pattern = ((IEnumerable<string>)source).Select<string, byte>((Func<string, byte>)(s => !s.Contains("??") ? byte.Parse(s, NumberStyles.HexNumber) : (byte)0)).ToArray<byte>(),
                mask = ((IEnumerable<string>)source).Select<string, byte>((Func<string, byte>)(s => !s.Contains("??") ? byte.MaxValue : (byte)0)).ToArray<byte>()
            };
        }

        public bool AobReplace(long address, string bytePattern)
        {
            try
            {
                byte[] byteArray = this.StringToByteArray(bytePattern);
                return Flame.WriteProcessMemory(this._processHandle, (IntPtr)address, byteArray, (IntPtr)byteArray.Length, IntPtr.Zero);
            }
            catch (Exception ex)
            {
            }
            return false;
        }

        public bool AobReplace(long address, int bytePattern)
        {
            byte[] bytes = BitConverter.GetBytes(bytePattern);
            return Flame.WriteProcessMemory(this._processHandle, (IntPtr)address, bytes, (IntPtr)bytes.Length, IntPtr.Zero);
        }

        public async Task<int> ReadIntAsync(long addressToRead)
        {
            return await Task.Run<int>((Func<int>)(() => this.ReadInt(addressToRead)));
        }

        public int ReadInt(long addressToRead)
        {
            byte[] lpBuffer = new byte[4];
            return Flame.ReadProcessMemory(this._processHandle, (IntPtr)addressToRead, lpBuffer, (IntPtr)lpBuffer.Length, out IntPtr _) ? BitConverter.ToInt32(lpBuffer, 0) : 0;
        }

        public float ReadFloat(long addressToRead)
        {
            byte[] lpBuffer = new byte[4];
            return Flame.ReadProcessMemory(this._processHandle, (IntPtr)addressToRead, lpBuffer, (IntPtr)lpBuffer.Length, out IntPtr _) ? BitConverter.ToSingle(lpBuffer, 0) : 0.0f;
        }

        public byte ReadHexByte(long addressToRead)
        {
            byte[] lpBuffer = new byte[1];
            return Flame.ReadProcessMemory(this._processHandle, (IntPtr)addressToRead, lpBuffer, (IntPtr)lpBuffer.Length, out IntPtr _) ? lpBuffer[0] : (byte)0;
        }

        public short ReadInt16(long addressToRead)
        {
            byte[] lpBuffer = new byte[2];
            return Flame.ReadProcessMemory(this._processHandle, (IntPtr)addressToRead, lpBuffer, (IntPtr)lpBuffer.Length, out IntPtr _) ? BitConverter.ToInt16(lpBuffer, 0) : (short)0;
        }

        public string ReadString(long addressToRead, int size)
        {
            byte[] lpBuffer = new byte[size];
            IntPtr lpNumberOfBytesRead;
            return Flame.ReadProcessMemory(this._processHandle, (IntPtr)addressToRead, lpBuffer, (IntPtr)size, out lpNumberOfBytesRead) && lpNumberOfBytesRead.ToInt64() == (long)size ? BitConverter.ToString(lpBuffer).Replace("-", " ") : "";
        }

        private byte[] StringToByteArray(string hexString)
        {
            return ((IEnumerable<string>)hexString.Split(' ')).Select<string, byte>((Func<string, byte>)(hex => byte.Parse(hex, NumberStyles.HexNumber))).ToArray<byte>();
        }

        private int FindPattern(byte[] body, byte[] pattern, byte[] masks, int start = 0)
        {
            int pattern1 = -1;
            if (body.Length == 0 || pattern.Length == 0 || start > body.Length - pattern.Length || pattern.Length > body.Length)
                return pattern1;
            for (int index1 = start; index1 <= body.Length - pattern.Length; ++index1)
            {
                if (((int)body[index1] & (int)masks[0]) == ((int)pattern[0] & (int)masks[0]))
                {
                    bool flag = true;
                    for (int index2 = pattern.Length - 1; index2 >= 1; --index2)
                    {
                        if (((int)body[index1 + index2] & (int)masks[index2]) != ((int)pattern[index2] & (int)masks[index2]))
                        {
                            flag = false;
                            break;
                        }
                    }
                    if (flag)
                    {
                        pattern1 = index1;
                        break;
                    }
                }
            }
            return pattern1;
        }

        public struct PatternData
        {
            public byte[] pattern { get; set; }

            public byte[] mask { get; set; }
        }

        public struct MemoryPage
        {
            public IntPtr Start;
            public int Size;

            public MemoryPage(IntPtr start, int size)
            {
                this.Start = start;
                this.Size = size;
            }
        }

        public struct MEMORY_BASIC_INFORMATION
        {
            public IntPtr BaseAddress;
            public IntPtr AllocationBase;
            public uint AllocationProtect;
            public UIntPtr RegionSize;
            public uint State;
            public uint Protect;
            public uint Type;
        }
    }
}
