<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>DllExport</id>
    <version>1.8.0</version>
    <title>.NET DllExport</title>
    <authors>github.com/3F/DllExport</authors>
    <license type="file">LICENSE.txt</license>
    <owners>reg</owners>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <projectUrl>https://github.com/3F/DllExport</projectUrl>
    <repository type="git" url="https://github.com/3F/DllExport" />
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>Open source .NET DllExport with .NET Core support (aka 3F/DllExport aka DllExport.bat) https://github.com/3F/DllExport
        
        🚀 Quick start: https://github.com/3F/DllExport/wiki/Quick-start

        🔖 Examples. Unmanaged C++ / C# / Java: https://youtu.be/QXMj9-8XJnY
        🧪 Demo: https://github.com/3F/Examples/tree/master/DllExport/BasicExport
        
            * https://github.com/3F/DllExport/tree/master/src/DllExport/assets
              https://github.com/3F/DllExport/tree/master/src/DllExport/UnitedTest

        ===
        DllExport -dxp-version 1.8.0
        https://3F.github.io/DllExport/releases/latest/manager/

        ===
        gnt DllExport/1.8.0
        https://github.com/3F/GetNuTool

  
    .NET DllExport 1.8.0+f4deb0e    
    
    Configuration:  PublicRelease    
    Release type:       
    Build number:   17264    
    toolset:        net40    
    MetaCor:        netstandard1.1    
    MetaLib:        net20    
    hMSBuild core:  2.4.1    
    
    :: generated by a vsSolutionBuildEvent 1.16.1.32816</description>
    <summary>.NET DllExport with .NET Core support (aka 3F/DllExport aka DllExport.bat)</summary>
    <tags>DllExport unmanaged-exports ildasm ilasm coreclr exported-functions dotnetcore Conari pinvoke net-c-func native tools dotnet-DllExport unmanaged-export hMSBuild GetNuTool MvsSln</tags>
    <releaseNotes> changelog: https://github.com/3F/DllExport/blob/master/changelog.txt </releaseNotes>
    <copyright>Copyright (c) 2009-2015  Robert Giesecke | Copyright (c) 2016-2025  Denis Kuzmin &lt;<EMAIL>&gt; github/3F </copyright>
  </metadata>
</package>