﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;

namespace AotForms
{
    internal struct WeaponInfo
    {
        public string Name { get; set; }
        public string IconPath { get; set; }
    }

    internal static class WeaponIndex
    {
        private static readonly string IconDirectory = @"C:\Users\<USER>\Downloads\ESP LINE\AotForms\Properties\WeaponIcons";

        // Questo metodo converte i pixel bianchi delle icone in lime
        internal static void ConvertIconsToLime()
        {
            if (!Directory.Exists(IconDirectory))
            {
                Console.WriteLine("La cartella delle icone non esiste.");
                return;
            }

            string[] files = Directory.GetFiles(IconDirectory, "*.png");

            foreach (var file in files)
            {
                try
                {
                    using (Bitmap bitmap = new Bitmap(file))
                    {
                        for (int y = 0; y < bitmap.Height; y++)
                        {
                            for (int x = 0; x < bitmap.Width; x++)
                            {
                                Color pixel = bitmap.GetPixel(x, y);

                                // Sostituisce il bianco (o quasi bianco) con lime
                                if (pixel.A > 0 && pixel.R > 230 && pixel.G > 230 && pixel.B > 230)
                                {
                                    bitmap.SetPixel(x, y, Color.Lime);
                                }
                            }
                        }

                        bitmap.Save(file); // Sovrascrive l'immagine originale
                        Console.WriteLine($"Convertita: {Path.GetFileName(file)}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Errore su {file}: {ex.Message}");
                }
            }

            Console.WriteLine("Conversione completata.");
        }

        internal static WeaponInfo GetWeaponInfo(int index)
        {
            string name = index switch
            {
                0 => "AK47",
                1 => "FIST",
                2 => "M4A1",
                3 => "USP",
                4 => "AWM",
                5 => "M1014",
                6 => "AK47",
                7 => "UMP",
                8 => "MP5",
                9 => "Desert-Eagle",
                10 => "G18",
                11 => "M14",
                12 => "SCAR",
                13 => "VSS",
                14 => "GROZA",
                15 => "MP40",
                16 => "PAN",
                17 => "PARANG",
                18 => "SKS",
                19 => "M249",
                20 => "M1873",
                21 => "KAR98K",
                24 => "FAMAS",
                25 => "M500",
                26 => "SVD",
                27 => "BAT",
                28 => "XM8",
                29 => "SPAS12",
                30 => "M60",
                32 => "P90",
                33 => "AN94",
                34 => "KATANA",
                35 => "CG15",
                39 => "PLASMA",
                41 => "M1887",
                43 => "THOMPSON",
                45 => "M82B",
                46 => "AUG",
                47 => "PARAFAL",
                48 => "WOODPECKER",
                49 => "VECTOR",
                50 => "MAG7",
                1201 => "GLOO WALL",
                51 => "SCYTHE",
                54 => "KORD",
                55 => "M1917",
                56 => "USP2",
                57 => "KINGFISHER",
                58 => "MINI-UZI",
                60 => "MP5",
                61 => "M60",
                62 => "VSS",
                63 => "M14",
                64 => "KAR98K",
                65 => "AWM-Y",
                67 => "FAMAS-I",
                70 => "GROZA",
                71 => "M249",
                72 => "SVD",
                73 => "G36",
                74 => "G36",
                75 => "M24",
                78 => "HEALSNIPER",
                80 => "M4A1",
                81 => "M4A1",
                82 => "M4A1",
                86 => "CHARGE BUSTER",
                88 => "MAC10",
                89 => "AC80",
                93 => "HEAL-PISTOL",
                99 => "SHIELD-GUN",
                100 => "FLAMTHROWER",
                119 => "M1887",
                120 => "MP5",
                121 => "MP5",
                122 => "M60",
                123 => "M60",
                124 => "VSS",
                125 => "VSS",
                126 => "M14",
                127 => "M14",
                128 => "KAR98K",
                129 => "KAR98K",
                130 => "FAMAS",
                131 => "FAMAS",
                150 => "BIZON",
                181 => "TROGON",
                197 => "VSK94",
                21002 => "M590",
                178 => "SCAR",
                179 => "SCAR",
                180 => "SCAR",
                193 => "AUG",
                194 => "AUG",
                195 => "AUG",
                228 => "MAC10",
                229 => "MAC10",
                230 => "MAC10",
                184 => "M1014",
                185 => "M1014",
                186 => "M1014",
                21001 => "HEAL-PISTOL",
                _ => $"Unknown Weapon [{index}]"
            };

            string iconPath = Path.Combine(IconDirectory, $"{name}.png");

            if (!File.Exists(iconPath))
            {
                iconPath = Path.Combine(IconDirectory, "default.png");
            }

            return new WeaponInfo
            {
                Name = name,
                IconPath = iconPath
            };
        }
    }
}