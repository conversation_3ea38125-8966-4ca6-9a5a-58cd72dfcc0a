using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading.Tasks;

namespace AotForms
{
    internal static class ErrorHandler
    {
        private static readonly ConcurrentQueue<string> _logQueue = new();
        private static readonly object _lockObject = new object();
        private static bool _isLoggingEnabled = true;
        private static string _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "error.log");

        static ErrorHandler()
        {
            // Start background logging task
            _ = Task.Run(ProcessLogQueue);
        }

        public static void LogError(string message, Exception ex = null)
        {
            if (!_isLoggingEnabled) return;

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var logMessage = ex != null 
                    ? $"[{timestamp}] ERROR: {message} - {ex.Message}\n{ex.StackTrace}"
                    : $"[{timestamp}] ERROR: {message}";

                _logQueue.Enqueue(logMessage);

                // Also output to console for debugging
                Console.WriteLine(logMessage);
            }
            catch
            {
                // Ignore logging errors to prevent infinite loops
            }
        }

        public static void LogWarning(string message)
        {
            if (!_isLoggingEnabled) return;

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var logMessage = $"[{timestamp}] WARNING: {message}";

                _logQueue.Enqueue(logMessage);
                Console.WriteLine(logMessage);
            }
            catch
            {
                // Ignore logging errors
            }
        }

        public static void LogInfo(string message)
        {
            if (!_isLoggingEnabled) return;

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var logMessage = $"[{timestamp}] INFO: {message}";

                _logQueue.Enqueue(logMessage);
                Console.WriteLine(logMessage);
            }
            catch
            {
                // Ignore logging errors
            }
        }

        private static async Task ProcessLogQueue()
        {
            while (true)
            {
                try
                {
                    if (_logQueue.TryDequeue(out var message))
                    {
                        await WriteToFile(message);
                    }
                    else
                    {
                        await Task.Delay(100); // Wait if no messages
                    }
                }
                catch
                {
                    // Continue processing even if one write fails
                    await Task.Delay(1000);
                }
            }
        }

        private static async Task WriteToFile(string message)
        {
            try
            {
                lock (_lockObject)
                {
                    File.AppendAllText(_logFilePath, message + Environment.NewLine);
                }

                // Keep log file size manageable
                var fileInfo = new FileInfo(_logFilePath);
                if (fileInfo.Exists && fileInfo.Length > 10 * 1024 * 1024) // 10MB
                {
                    RotateLogFile();
                }
            }
            catch
            {
                // Ignore file write errors
            }
        }

        private static void RotateLogFile()
        {
            try
            {
                var backupPath = _logFilePath + ".old";
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }
                File.Move(_logFilePath, backupPath);
            }
            catch
            {
                // If rotation fails, just delete the current log
                try
                {
                    File.Delete(_logFilePath);
                }
                catch
                {
                    // Ignore
                }
            }
        }

        public static void SetLoggingEnabled(bool enabled)
        {
            _isLoggingEnabled = enabled;
        }

        public static void ClearLogs()
        {
            try
            {
                lock (_lockObject)
                {
                    if (File.Exists(_logFilePath))
                    {
                        File.Delete(_logFilePath);
                    }
                }
            }
            catch
            {
                // Ignore
            }
        }
    }

    // Performance monitor class
    internal static class PerformanceMonitor
    {
        private static readonly ConcurrentDictionary<string, PerformanceCounter> _counters = new();

        public class PerformanceCounter
        {
            public long TotalCalls { get; set; }
            public long TotalTime { get; set; }
            public long MaxTime { get; set; }
            public long MinTime { get; set; } = long.MaxValue;
            public DateTime LastCall { get; set; }

            public double AverageTime => TotalCalls > 0 ? (double)TotalTime / TotalCalls : 0;
        }

        public static IDisposable StartMeasurement(string operationName)
        {
            return new PerformanceMeasurement(operationName);
        }

        private class PerformanceMeasurement : IDisposable
        {
            private readonly string _operationName;
            private readonly System.Diagnostics.Stopwatch _stopwatch;

            public PerformanceMeasurement(string operationName)
            {
                _operationName = operationName;
                _stopwatch = System.Diagnostics.Stopwatch.StartNew();
            }

            public void Dispose()
            {
                _stopwatch.Stop();
                var elapsed = _stopwatch.ElapsedMilliseconds;

                var counter = _counters.GetOrAdd(_operationName, _ => new PerformanceCounter());
                
                counter.TotalCalls++;
                counter.TotalTime += elapsed;
                counter.LastCall = DateTime.Now;
                
                if (elapsed > counter.MaxTime)
                    counter.MaxTime = elapsed;
                
                if (elapsed < counter.MinTime)
                    counter.MinTime = elapsed;

                // Log slow operations
                if (elapsed > 100) // More than 100ms
                {
                    ErrorHandler.LogWarning($"Slow operation: {_operationName} took {elapsed}ms");
                }
            }
        }

        public static PerformanceCounter GetCounter(string operationName)
        {
            return _counters.TryGetValue(operationName, out var counter) ? counter : null;
        }

        public static void LogPerformanceStats()
        {
            foreach (var kvp in _counters)
            {
                var counter = kvp.Value;
                ErrorHandler.LogInfo($"Performance [{kvp.Key}]: Calls={counter.TotalCalls}, " +
                    $"Avg={counter.AverageTime:F2}ms, Max={counter.MaxTime}ms, Min={counter.MinTime}ms");
            }
        }
    }
}
