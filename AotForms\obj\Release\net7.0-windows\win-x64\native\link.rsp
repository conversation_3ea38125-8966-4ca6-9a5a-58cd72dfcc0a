﻿"obj\Release\net7.0-windows\win-x64\native\shg.obj"
/OUT:"bin\Release\net7.0-windows\win-x64\native\shg.dll"
/DEF:"obj\Release\net7.0-windows\win-x64\native\shg.def"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\lib\um\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64"
"obj\Release\net7.0-windows\win-x64\native\shg.res"
/DLL
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\bootstrapperdll.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\Runtime.WorkstationGC.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.Globalization.Native.Aot.lib"
"C:\Users\<USER>\.nuget\packages\runtime.win-x64.microsoft.dotnet.ilcompiler\7.0.20\sdk\System.IO.Compression.Native.Aot.lib"
"advapi32.lib"
"bcrypt.lib"
"crypt32.lib"
"iphlpapi.lib"
"kernel32.lib"
"mswsock.lib"
"ncrypt.lib"
"normaliz.lib"
"ntdll.lib"
"ole32.lib"
"oleaut32.lib"
"secur32.lib"
"user32.lib"
"version.lib"
"ws2_32.lib"
/NOLOGO /MANIFEST:NO
/DEBUG
/INCREMENTAL:NO
/INCLUDE:NativeAOT_StaticInitialization
/NATVIS:"C:\Users\<USER>\.nuget\packages\microsoft.dotnet.ilcompiler\7.0.20\build\NativeAOT.natvis"
/NODEFAULTLIB:libucrt.lib
/DEFAULTLIB:ucrt.lib
/OPT:REF
/OPT:ICF
