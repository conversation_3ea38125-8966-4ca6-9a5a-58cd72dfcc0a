using System;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace AotForms
{
    /// <summary>
    /// مراقب العمليات للكشف عن التعليق والانقطاع
    /// </summary>
    internal static class ProcessMonitor
    {
        private static Timer _monitorTimer;
        private static DateTime _lastDataUpdate = DateTime.Now;
        private static DateTime _lastAimbotUpdate = DateTime.Now;
        private static DateTime _lastESPUpdate = DateTime.Now;
        private static bool _isMonitoring = false;
        private static readonly object _lockObject = new object();

        // عدادات للكشف عن التعليق
        private static int _dataHeartbeat = 0;
        private static int _aimbotHeartbeat = 0;
        private static int _espHeartbeat = 0;

        /// <summary>
        /// بدء مراقبة العمليات
        /// </summary>
        public static void StartMonitoring()
        {
            lock (_lockObject)
            {
                if (_isMonitoring) return;

                try
                {
                    _monitorTimer = new Timer(CheckProcessHealth, null,
                        TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));

                    _isMonitoring = true;
                    ErrorHandler.LogInfo("Process monitoring started");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Failed to start process monitoring", ex);
                }
            }
        }

        /// <summary>
        /// إيقاف مراقبة العمليات
        /// </summary>
        public static void StopMonitoring()
        {
            lock (_lockObject)
            {
                if (!_isMonitoring) return;

                try
                {
                    _monitorTimer?.Dispose();
                    _monitorTimer = null;
                    _isMonitoring = false;
                    ErrorHandler.LogInfo("Process monitoring stopped");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Error stopping process monitoring", ex);
                }
            }
        }

        /// <summary>
        /// تحديث نبضة Data
        /// </summary>
        public static void UpdateDataHeartbeat()
        {
            _lastDataUpdate = DateTime.Now;
            Interlocked.Increment(ref _dataHeartbeat);
        }

        /// <summary>
        /// تحديث نبضة Aimbot
        /// </summary>
        public static void UpdateAimbotHeartbeat()
        {
            _lastAimbotUpdate = DateTime.Now;
            Interlocked.Increment(ref _aimbotHeartbeat);
        }

        /// <summary>
        /// تحديث نبضة ESP
        /// </summary>
        public static void UpdateESPHeartbeat()
        {
            _lastESPUpdate = DateTime.Now;
            Interlocked.Increment(ref _espHeartbeat);
        }

        /// <summary>
        /// فحص صحة العمليات
        /// </summary>
        private static void CheckProcessHealth(object state)
        {
            try
            {
                var now = DateTime.Now;
                var timeout = TimeSpan.FromSeconds(30); // مهلة 30 ثانية

                // فحص Data
                if (now - _lastDataUpdate > timeout)
                {
                    ErrorHandler.LogWarning($"Data process appears to be stuck. Last update: {_lastDataUpdate}");
                    TriggerDataRecovery();
                }

                // فحص Aimbot
                if (now - _lastAimbotUpdate > timeout && Config.AimBot)
                {
                    ErrorHandler.LogWarning($"Aimbot process appears to be stuck. Last update: {_lastAimbotUpdate}");
                    TriggerAimbotRecovery();
                }

                // فحص ESP
                if (now - _lastESPUpdate > timeout)
                {
                    ErrorHandler.LogWarning($"ESP process appears to be stuck. Last update: {_lastESPUpdate}");
                    TriggerESPRecovery();
                }

                // فحص استهلاك الذاكرة
                var memoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
                if (memoryMB > PerformanceConfig.MemoryPressureThresholdMB * 2)
                {
                    ErrorHandler.LogWarning($"Critical memory usage detected: {memoryMB}MB");
                    TriggerMemoryRecovery();
                }

                // تسجيل الإحصائيات
                LogProcessStatistics();
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error in process health check", ex);
            }
        }

        /// <summary>
        /// استعادة عملية Data
        /// </summary>
        private static void TriggerDataRecovery()
        {
            try
            {
                ErrorHandler.LogInfo("Triggering Data process recovery");
                
                // تنظيف الكاش
                InternalMemory.ClearCache();
                Core.Entities?.Clear();
                
                // إعادة تعيين الوقت
                _lastDataUpdate = DateTime.Now;
                
                // تشغيل تنظيف الذاكرة
                _ = Task.Run(() =>
                {
                    GC.Collect(1, GCCollectionMode.Optimized);
                    GC.WaitForPendingFinalizers();
                });
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error in Data recovery", ex);
            }
        }

        /// <summary>
        /// استعادة عملية Aimbot
        /// </summary>
        private static void TriggerAimbotRecovery()
        {
            try
            {
                ErrorHandler.LogInfo("Triggering Aimbot process recovery");
                _lastAimbotUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error in Aimbot recovery", ex);
            }
        }

        /// <summary>
        /// استعادة عملية ESP
        /// </summary>
        private static void TriggerESPRecovery()
        {
            try
            {
                ErrorHandler.LogInfo("Triggering ESP process recovery");
                _lastESPUpdate = DateTime.Now;
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error in ESP recovery", ex);
            }
        }

        /// <summary>
        /// استعادة الذاكرة
        /// </summary>
        private static void TriggerMemoryRecovery()
        {
            try
            {
                ErrorHandler.LogInfo("Triggering emergency memory recovery");
                
                // تنظيف شامل
                InternalMemory.ClearCache();
                Core.Entities?.Clear();
                
                // تنظيف قوي للذاكرة
                GC.Collect(2, GCCollectionMode.Forced);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Forced);
                
                var afterMemory = GC.GetTotalMemory(true);
                ErrorHandler.LogInfo($"Memory recovery completed. Current usage: {afterMemory / 1024 / 1024}MB");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error in memory recovery", ex);
            }
        }

        /// <summary>
        /// تسجيل إحصائيات العمليات
        /// </summary>
        private static void LogProcessStatistics()
        {
            try
            {
                var memoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
                var cacheSize = InternalMemory.GetCacheSize();
                var entityCount = Core.Entities?.Count ?? 0;

                ErrorHandler.LogInfo($"Process Stats - Memory: {memoryMB}MB, Cache: {cacheSize}, Entities: {entityCount}");
                ErrorHandler.LogInfo($"Heartbeats - Data: {_dataHeartbeat}, Aimbot: {_aimbotHeartbeat}, ESP: {_espHeartbeat}");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error logging process statistics", ex);
            }
        }

        /// <summary>
        /// الحصول على حالة المراقبة
        /// </summary>
        public static bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// الحصول على آخر تحديث للعمليات
        /// </summary>
        public static string GetLastUpdateInfo()
        {
            return $"Data: {_lastDataUpdate:HH:mm:ss}, Aimbot: {_lastAimbotUpdate:HH:mm:ss}, ESP: {_lastESPUpdate:HH:mm:ss}";
        }
    }
}
