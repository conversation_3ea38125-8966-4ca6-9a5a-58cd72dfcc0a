﻿using Guna.UI2.WinForms.Suite;

namespace Client
{
    partial class AimbotForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            CustomizableEdges customizableEdges13 = new CustomizableEdges();
            CustomizableEdges customizableEdges14 = new CustomizableEdges();
            CustomizableEdges customizableEdges9 = new CustomizableEdges();
            CustomizableEdges customizableEdges10 = new CustomizableEdges();
            CustomizableEdges customizableEdges11 = new CustomizableEdges();
            CustomizableEdges customizableEdges12 = new CustomizableEdges();
            CustomizableEdges customizableEdges7 = new CustomizableEdges();
            CustomizableEdges customizableEdges8 = new CustomizableEdges();
            CustomizableEdges customizableEdges5 = new CustomizableEdges();
            CustomizableEdges customizableEdges6 = new CustomizableEdges();
            CustomizableEdges customizableEdges3 = new CustomizableEdges();
            CustomizableEdges customizableEdges4 = new CustomizableEdges();
            CustomizableEdges customizableEdges15 = new CustomizableEdges();
            CustomizableEdges customizableEdges16 = new CustomizableEdges();
            CustomizableEdges customizableEdges1 = new CustomizableEdges();
            CustomizableEdges customizableEdges2 = new CustomizableEdges();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(AimbotForm));
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            label1 = new Label();
            guna2ToggleSwitch1 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            guna2ToggleSwitch6 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label6 = new Label();
            guna2PictureBox7 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2PictureBox8 = new Guna.UI2.WinForms.Guna2PictureBox();
            label7 = new Label();
            guna2TrackBar1 = new Guna.UI2.WinForms.Guna2TrackBar();
            guna2TrackBar2 = new Guna.UI2.WinForms.Guna2TrackBar();
            label8 = new Label();
            guna2PictureBox9 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2TrackBar3 = new Guna.UI2.WinForms.Guna2TrackBar();
            label9 = new Label();
            guna2PictureBox10 = new Guna.UI2.WinForms.Guna2PictureBox();
            aimfovtxt = new Label();
            lblDistance = new Label();
            rangeaim = new Label();
            guna2DragControl1 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2PictureBox2 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2DragControl3 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            pictureBox1 = new PictureBox();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox8).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox9).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox10).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // guna2BorderlessForm1
            // 
            guna2BorderlessForm1.BorderRadius = 20;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockForm = false;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 1D;
            guna2BorderlessForm1.DragStartTransparencyValue = 1D;
            guna2BorderlessForm1.HasFormShadow = false;
            guna2BorderlessForm1.ResizeForm = false;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.BackColor = Color.FromArgb(21, 21, 21);
            label1.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label1.ForeColor = Color.Silver;
            label1.Location = new Point(22, 68);
            label1.Name = "label1";
            label1.Size = new Size(73, 15);
            label1.TabIndex = 8;
            label1.Text = "AIMBOT AI";
            // 
            // guna2ToggleSwitch1
            // 
            guna2ToggleSwitch1.Animated = true;
            guna2ToggleSwitch1.AutoRoundedCorners = true;
            guna2ToggleSwitch1.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch1.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch1.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch1.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch1.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch1.CustomizableEdges = customizableEdges13;
            guna2ToggleSwitch1.Location = new Point(227, 62);
            guna2ToggleSwitch1.Name = "guna2ToggleSwitch1";
            guna2ToggleSwitch1.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2ToggleSwitch1.Size = new Size(43, 21);
            guna2ToggleSwitch1.TabIndex = 39;
            guna2ToggleSwitch1.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch1.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch1.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch1.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch1.CheckedChanged += guna2ToggleSwitch1_CheckedChanged;
            // 
            // guna2ToggleSwitch6
            // 
            guna2ToggleSwitch6.Animated = true;
            guna2ToggleSwitch6.AutoRoundedCorners = true;
            guna2ToggleSwitch6.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch6.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch6.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch6.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch6.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch6.CustomizableEdges = customizableEdges9;
            guna2ToggleSwitch6.Location = new Point(227, 116);
            guna2ToggleSwitch6.Name = "guna2ToggleSwitch6";
            guna2ToggleSwitch6.ShadowDecoration.CustomizableEdges = customizableEdges10;
            guna2ToggleSwitch6.Size = new Size(43, 21);
            guna2ToggleSwitch6.TabIndex = 54;
            guna2ToggleSwitch6.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch6.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch6.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch6.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch6.CheckedChanged += guna2ToggleSwitch6_CheckedChanged;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.BackColor = Color.FromArgb(21, 21, 21);
            label6.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label6.ForeColor = Color.Silver;
            label6.Location = new Point(22, 116);
            label6.Name = "label6";
            label6.Size = new Size(116, 15);
            label6.TabIndex = 52;
            label6.Text = "SHOW FOV CIRCLE";
            // 
            // guna2PictureBox7
            // 
            guna2PictureBox7.BorderRadius = 5;
            guna2PictureBox7.CustomizableEdges = customizableEdges11;
            guna2PictureBox7.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox7.ImageRotate = 0F;
            guna2PictureBox7.Location = new Point(12, 103);
            guna2PictureBox7.Name = "guna2PictureBox7";
            guna2PictureBox7.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2PictureBox7.Size = new Size(266, 42);
            guna2PictureBox7.TabIndex = 53;
            guna2PictureBox7.TabStop = false;
            // 
            // guna2PictureBox8
            // 
            guna2PictureBox8.BorderRadius = 5;
            guna2PictureBox8.CustomizableEdges = customizableEdges7;
            guna2PictureBox8.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox8.ImageRotate = 0F;
            guna2PictureBox8.Location = new Point(13, 151);
            guna2PictureBox8.Name = "guna2PictureBox8";
            guna2PictureBox8.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2PictureBox8.Size = new Size(266, 54);
            guna2PictureBox8.TabIndex = 55;
            guna2PictureBox8.TabStop = false;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.BackColor = Color.FromArgb(21, 21, 21);
            label7.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label7.ForeColor = Color.Silver;
            label7.Location = new Point(20, 156);
            label7.Name = "label7";
            label7.Size = new Size(58, 15);
            label7.TabIndex = 56;
            label7.Text = "AIM FOV";
            // 
            // guna2TrackBar1
            // 
            guna2TrackBar1.BackColor = Color.FromArgb(21, 21, 21);
            guna2TrackBar1.FillColor = Color.FromArgb(43, 43, 43);
            guna2TrackBar1.LargeChange = 1;
            guna2TrackBar1.Location = new Point(21, 175);
            guna2TrackBar1.Maximum = 1000;
            guna2TrackBar1.MouseWheelBarPartitions = 1;
            guna2TrackBar1.Name = "guna2TrackBar1";
            guna2TrackBar1.Size = new Size(251, 23);
            guna2TrackBar1.TabIndex = 57;
            guna2TrackBar1.ThumbColor = Color.Cyan;
            guna2TrackBar1.Value = 100;
            guna2TrackBar1.Scroll += guna2TrackBar1_Scroll;
            // 
            // guna2TrackBar2
            // 
            guna2TrackBar2.BackColor = Color.FromArgb(21, 21, 21);
            guna2TrackBar2.FillColor = Color.FromArgb(43, 43, 43);
            guna2TrackBar2.LargeChange = 1;
            guna2TrackBar2.Location = new Point(21, 235);
            guna2TrackBar2.MouseWheelBarPartitions = 1;
            guna2TrackBar2.Name = "guna2TrackBar2";
            guna2TrackBar2.Size = new Size(251, 23);
            guna2TrackBar2.TabIndex = 60;
            guna2TrackBar2.ThumbColor = Color.Cyan;
            guna2TrackBar2.Value = 0;
            guna2TrackBar2.Scroll += guna2TrackBar2_Scroll;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.BackColor = Color.FromArgb(21, 21, 21);
            label8.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label8.ForeColor = Color.Silver;
            label8.Location = new Point(20, 216);
            label8.Name = "label8";
            label8.Size = new Size(74, 15);
            label8.TabIndex = 59;
            label8.Text = "AIM DELAY";
            // 
            // guna2PictureBox9
            // 
            guna2PictureBox9.BorderRadius = 5;
            guna2PictureBox9.CustomizableEdges = customizableEdges5;
            guna2PictureBox9.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox9.ImageRotate = 0F;
            guna2PictureBox9.Location = new Point(13, 212);
            guna2PictureBox9.Name = "guna2PictureBox9";
            guna2PictureBox9.ShadowDecoration.CustomizableEdges = customizableEdges6;
            guna2PictureBox9.Size = new Size(266, 54);
            guna2PictureBox9.TabIndex = 58;
            guna2PictureBox9.TabStop = false;
            guna2PictureBox9.Click += guna2PictureBox9_Click;
            // 
            // guna2TrackBar3
            // 
            guna2TrackBar3.BackColor = Color.FromArgb(21, 21, 21);
            guna2TrackBar3.FillColor = Color.FromArgb(43, 43, 43);
            guna2TrackBar3.LargeChange = 1;
            guna2TrackBar3.Location = new Point(21, 295);
            guna2TrackBar3.Maximum = 150;
            guna2TrackBar3.MouseWheelBarPartitions = 1;
            guna2TrackBar3.Name = "guna2TrackBar3";
            guna2TrackBar3.Size = new Size(251, 23);
            guna2TrackBar3.TabIndex = 63;
            guna2TrackBar3.ThumbColor = Color.Cyan;
            guna2TrackBar3.Value = 100;
            guna2TrackBar3.Scroll += guna2TrackBar3_Scroll;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.BackColor = Color.FromArgb(21, 21, 21);
            label9.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label9.ForeColor = Color.Silver;
            label9.Location = new Point(20, 276);
            label9.Name = "label9";
            label9.Size = new Size(78, 15);
            label9.TabIndex = 62;
            label9.Text = "AIM RANGE";
            // 
            // guna2PictureBox10
            // 
            guna2PictureBox10.BorderRadius = 5;
            guna2PictureBox10.CustomizableEdges = customizableEdges3;
            guna2PictureBox10.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox10.ImageRotate = 0F;
            guna2PictureBox10.Location = new Point(13, 272);
            guna2PictureBox10.Name = "guna2PictureBox10";
            guna2PictureBox10.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2PictureBox10.Size = new Size(266, 54);
            guna2PictureBox10.TabIndex = 61;
            guna2PictureBox10.TabStop = false;
            // 
            // aimfovtxt
            // 
            aimfovtxt.AutoSize = true;
            aimfovtxt.BackColor = Color.FromArgb(21, 21, 21);
            aimfovtxt.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            aimfovtxt.ForeColor = Color.Silver;
            aimfovtxt.Location = new Point(233, 156);
            aimfovtxt.Name = "aimfovtxt";
            aimfovtxt.Size = new Size(37, 15);
            aimfovtxt.TabIndex = 64;
            aimfovtxt.Text = "(100)";
            // 
            // lblDistance
            // 
            lblDistance.AutoSize = true;
            lblDistance.BackColor = Color.FromArgb(21, 21, 21);
            lblDistance.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            lblDistance.ForeColor = Color.Silver;
            lblDistance.Location = new Point(233, 216);
            lblDistance.Name = "lblDistance";
            lblDistance.Size = new Size(24, 15);
            lblDistance.TabIndex = 65;
            lblDistance.Text = "(0)";
            // 
            // rangeaim
            // 
            rangeaim.AutoSize = true;
            rangeaim.BackColor = Color.FromArgb(21, 21, 21);
            rangeaim.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            rangeaim.ForeColor = Color.Silver;
            rangeaim.Location = new Point(233, 276);
            rangeaim.Name = "rangeaim";
            rangeaim.Size = new Size(37, 15);
            rangeaim.TabIndex = 66;
            rangeaim.Text = "(100)";
            // 
            // guna2DragControl1
            // 
            guna2DragControl1.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl1.UseTransparentDrag = true;
            // 
            // guna2PictureBox2
            // 
            guna2PictureBox2.BorderRadius = 5;
            guna2PictureBox2.CustomizableEdges = customizableEdges15;
            guna2PictureBox2.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox2.ImageRotate = 0F;
            guna2PictureBox2.Location = new Point(12, 55);
            guna2PictureBox2.Name = "guna2PictureBox2";
            guna2PictureBox2.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2PictureBox2.Size = new Size(266, 42);
            guna2PictureBox2.TabIndex = 9;
            guna2PictureBox2.TabStop = false;
            // 
            // guna2DragControl3
            // 
            guna2DragControl3.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl3.UseTransparentDrag = true;
            // 
            // guna2Button1
            // 
            guna2Button1.Animated = true;
            guna2Button1.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button1.BorderRadius = 5;
            guna2Button1.CustomizableEdges = customizableEdges1;
            guna2Button1.DisabledState.BorderColor = Color.DarkGray;
            guna2Button1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button1.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button1.FillColor = Color.White;
            guna2Button1.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button1.ForeColor = Color.White;
            guna2Button1.Location = new Point(199, 115);
            guna2Button1.Name = "guna2Button1";
            guna2Button1.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2Button1.Size = new Size(22, 22);
            guna2Button1.TabIndex = 68;
            guna2Button1.Click += guna2Button1_Click;
            // 
            // pictureBox1
            // 
            pictureBox1.Dock = DockStyle.Top;
            pictureBox1.Image = (Image)resources.GetObject("pictureBox1.Image");
            pictureBox1.Location = new Point(0, 0);
            pictureBox1.Margin = new Padding(4, 3, 4, 3);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(291, 52);
            pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
            pictureBox1.TabIndex = 69;
            pictureBox1.TabStop = false;
            // 
            // AimbotForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(8, 8, 8);
            ClientSize = new Size(291, 336);
            Controls.Add(pictureBox1);
            Controls.Add(guna2Button1);
            Controls.Add(rangeaim);
            Controls.Add(lblDistance);
            Controls.Add(aimfovtxt);
            Controls.Add(guna2TrackBar3);
            Controls.Add(label9);
            Controls.Add(guna2PictureBox10);
            Controls.Add(guna2TrackBar2);
            Controls.Add(label8);
            Controls.Add(guna2PictureBox9);
            Controls.Add(guna2TrackBar1);
            Controls.Add(label7);
            Controls.Add(guna2PictureBox8);
            Controls.Add(guna2ToggleSwitch6);
            Controls.Add(label6);
            Controls.Add(guna2PictureBox7);
            Controls.Add(guna2ToggleSwitch1);
            Controls.Add(label1);
            Controls.Add(guna2PictureBox2);
            FormBorderStyle = FormBorderStyle.None;
            Name = "AimbotForm";
            ShowIcon = false;
            ShowInTaskbar = false;
            Text = "AimbotForm";
            Load += AimbotForm_Load;
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox8).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox9).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox10).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private Label label1;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch1;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox8;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch6;
        private Label label6;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox7;
        private Guna.UI2.WinForms.Guna2TrackBar guna2TrackBar1;
        private Label label7;
        private Label aimfovtxt;
        private Guna.UI2.WinForms.Guna2TrackBar guna2TrackBar3;
        private Label label9;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox10;
        private Guna.UI2.WinForms.Guna2TrackBar guna2TrackBar2;
        private Label label8;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox9;
        private Label rangeaim;
        private Label lblDistance;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl1;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox2;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl3;
        private Guna.UI2.WinForms.Guna2Button guna2Button1;
        private PictureBox pictureBox1;
    }
}