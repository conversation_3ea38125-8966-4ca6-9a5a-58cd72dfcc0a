using System;

namespace AotForms
{
    /// <summary>
    /// Configuration class for performance settings
    /// </summary>
    internal static class PerformanceConfig
    {
        // Frame rate settings
        public static int AimbotTargetFPS { get; set; } = 60;
        public static int DataCollectionTargetFPS { get; set; } = 30;
        public static int ESPTargetFPS { get; set; } = 60;

        // Entity processing limits
        public static int MaxEntitiesPerFrame { get; set; } = 50;
        public static int EntityBatchSize { get; set; } = 10;

        // Memory management
        public static int MaxCacheSize { get; set; } = 10000;
        public static int GCIntervalMinutes { get; set; } = 2;
        public static int MemoryPressureThresholdMB { get; set; } = 500;

        // Error handling
        public static int MaxErrorsBeforeReset { get; set; } = 10;
        public static int ErrorDelayMultiplier { get; set; } = 10;
        public static int MaxErrorDelay { get; set; } = 1000;

        // Performance monitoring
        public static int SlowOperationThresholdMs { get; set; } = 100;
        public static int PerformanceLogIntervalMinutes { get; set; } = 5;

        // Threading
        public static int TaskDelayOnDisabled { get; set; } = 50;
        public static int TaskDelayOnKeyNotPressed { get; set; } = 10;
        public static int TaskDelayOnError { get; set; } = 100;

        // Logging
        public static bool EnableLogging { get; set; } = true;
        public static int MaxLogFileSizeMB { get; set; } = 10;
        public static string LogFileName { get; set; } = "error.log";

        // String reading limits
        public static int MaxStringLength { get; set; } = 1024;

        // Cache settings
        public static bool EnableCache { get; set; } = true;
        public static int CacheCleanupIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// Get target frame time in milliseconds for given FPS
        /// </summary>
        public static int GetTargetFrameTime(int targetFPS)
        {
            return targetFPS > 0 ? 1000 / targetFPS : 16; // Default to ~60 FPS
        }

        /// <summary>
        /// Apply performance optimizations based on system capabilities
        /// </summary>
        public static void OptimizeForSystem()
        {
            try
            {
                // Get system info
                var totalMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
                var processorCount = Environment.ProcessorCount;

                // Adjust settings based on system capabilities
                if (processorCount >= 8) // High-end system
                {
                    AimbotTargetFPS = 120;
                    DataCollectionTargetFPS = 60;
                    MaxEntitiesPerFrame = 100;
                    EntityBatchSize = 20;
                }
                else if (processorCount >= 4) // Mid-range system
                {
                    AimbotTargetFPS = 60;
                    DataCollectionTargetFPS = 30;
                    MaxEntitiesPerFrame = 50;
                    EntityBatchSize = 10;
                }
                else // Low-end system
                {
                    AimbotTargetFPS = 30;
                    DataCollectionTargetFPS = 20;
                    MaxEntitiesPerFrame = 25;
                    EntityBatchSize = 5;
                    
                    // Reduce memory usage
                    MaxCacheSize = 5000;
                    GCIntervalMinutes = 1;
                }

                // Adjust memory settings
                if (totalMemoryMB < 100) // Low memory
                {
                    MaxCacheSize = Math.Min(MaxCacheSize, 2000);
                    MemoryPressureThresholdMB = 200;
                    GCIntervalMinutes = 1;
                }

                ErrorHandler.LogInfo($"Performance optimized for {processorCount} cores, {totalMemoryMB}MB memory");
                ErrorHandler.LogInfo($"Settings: Aimbot={AimbotTargetFPS}fps, Data={DataCollectionTargetFPS}fps, " +
                    $"MaxEntities={MaxEntitiesPerFrame}, Cache={MaxCacheSize}");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error optimizing performance settings", ex);
            }
        }

        /// <summary>
        /// Reset all settings to default values
        /// </summary>
        public static void ResetToDefaults()
        {
            AimbotTargetFPS = 60;
            DataCollectionTargetFPS = 30;
            ESPTargetFPS = 60;
            MaxEntitiesPerFrame = 50;
            EntityBatchSize = 10;
            MaxCacheSize = 10000;
            GCIntervalMinutes = 2;
            MemoryPressureThresholdMB = 500;
            MaxErrorsBeforeReset = 10;
            ErrorDelayMultiplier = 10;
            MaxErrorDelay = 1000;
            SlowOperationThresholdMs = 100;
            PerformanceLogIntervalMinutes = 5;
            TaskDelayOnDisabled = 50;
            TaskDelayOnKeyNotPressed = 10;
            TaskDelayOnError = 100;
            EnableLogging = true;
            MaxLogFileSizeMB = 10;
            LogFileName = "error.log";
            MaxStringLength = 1024;
            EnableCache = true;
            CacheCleanupIntervalMinutes = 5;

            ErrorHandler.LogInfo("Performance settings reset to defaults");
        }

        /// <summary>
        /// Validate and fix invalid settings
        /// </summary>
        public static void ValidateSettings()
        {
            // Ensure positive values
            AimbotTargetFPS = Math.Max(1, Math.Min(240, AimbotTargetFPS));
            DataCollectionTargetFPS = Math.Max(1, Math.Min(120, DataCollectionTargetFPS));
            ESPTargetFPS = Math.Max(1, Math.Min(240, ESPTargetFPS));
            
            MaxEntitiesPerFrame = Math.Max(1, Math.Min(200, MaxEntitiesPerFrame));
            EntityBatchSize = Math.Max(1, Math.Min(50, EntityBatchSize));
            
            MaxCacheSize = Math.Max(100, Math.Min(50000, MaxCacheSize));
            GCIntervalMinutes = Math.Max(1, Math.Min(60, GCIntervalMinutes));
            MemoryPressureThresholdMB = Math.Max(50, Math.Min(2000, MemoryPressureThresholdMB));
            
            MaxErrorsBeforeReset = Math.Max(1, Math.Min(100, MaxErrorsBeforeReset));
            ErrorDelayMultiplier = Math.Max(1, Math.Min(100, ErrorDelayMultiplier));
            MaxErrorDelay = Math.Max(100, Math.Min(10000, MaxErrorDelay));
            
            SlowOperationThresholdMs = Math.Max(10, Math.Min(5000, SlowOperationThresholdMs));
            PerformanceLogIntervalMinutes = Math.Max(1, Math.Min(60, PerformanceLogIntervalMinutes));
            
            TaskDelayOnDisabled = Math.Max(1, Math.Min(1000, TaskDelayOnDisabled));
            TaskDelayOnKeyNotPressed = Math.Max(1, Math.Min(100, TaskDelayOnKeyNotPressed));
            TaskDelayOnError = Math.Max(10, Math.Min(5000, TaskDelayOnError));
            
            MaxLogFileSizeMB = Math.Max(1, Math.Min(100, MaxLogFileSizeMB));
            MaxStringLength = Math.Max(64, Math.Min(4096, MaxStringLength));
            CacheCleanupIntervalMinutes = Math.Max(1, Math.Min(60, CacheCleanupIntervalMinutes));

            // Ensure batch size doesn't exceed max entities
            EntityBatchSize = Math.Min(EntityBatchSize, MaxEntitiesPerFrame);
        }

        /// <summary>
        /// Get current configuration as string for logging
        /// </summary>
        public static string GetConfigurationSummary()
        {
            return $"Performance Configuration:\n" +
                   $"  Frame Rates: Aimbot={AimbotTargetFPS}fps, Data={DataCollectionTargetFPS}fps, ESP={ESPTargetFPS}fps\n" +
                   $"  Entity Processing: Max={MaxEntitiesPerFrame}, Batch={EntityBatchSize}\n" +
                   $"  Memory: Cache={MaxCacheSize}, GC={GCIntervalMinutes}min, Threshold={MemoryPressureThresholdMB}MB\n" +
                   $"  Error Handling: MaxErrors={MaxErrorsBeforeReset}, Delay={ErrorDelayMultiplier}x, Max={MaxErrorDelay}ms\n" +
                   $"  Monitoring: SlowOp={SlowOperationThresholdMs}ms, Log={PerformanceLogIntervalMinutes}min\n" +
                   $"  Threading: Disabled={TaskDelayOnDisabled}ms, NoKey={TaskDelayOnKeyNotPressed}ms, Error={TaskDelayOnError}ms\n" +
                   $"  Logging: Enabled={EnableLogging}, MaxSize={MaxLogFileSizeMB}MB, File={LogFileName}\n" +
                   $"  Misc: MaxString={MaxStringLength}, Cache={EnableCache}, Cleanup={CacheCleanupIntervalMinutes}min";
        }
    }
}
