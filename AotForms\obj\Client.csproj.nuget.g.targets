﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.dotnet.ilcompiler\7.0.20\build\Microsoft.DotNet.ILCompiler.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.dotnet.ilcompiler\7.0.20\build\Microsoft.DotNet.ILCompiler.targets')" />
    <Import Project="$(NuGetPackageRoot)fody\6.8.2\build\Fody.targets" Condition="Exists('$(NuGetPackageRoot)fody\6.8.2\build\Fody.targets')" />
    <Import Project="$(NuGetPackageRoot)dnne\2.0.6\build\DNNE.targets" Condition="Exists('$(NuGetPackageRoot)dnne\2.0.6\build\DNNE.targets')" />
    <Import Project="$(NuGetPackageRoot)costura.fody\6.0.0\build\Costura.Fody.targets" Condition="Exists('$(NuGetPackageRoot)costura.fody\6.0.0\build\Costura.Fody.targets')" />
  </ImportGroup>
</Project>