﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace AotForms
{
    public class ParticleSystem : IDisposable
    {
        #region Configurazione
        private const int PARTICLE_COUNT = 15;
        private const int MAX_CONNECTION_DISTANCE = 85;
        private const int CONNECTION_ALPHA_MIN = 30;
        private const int CONNECTION_ALPHA_MAX = 110;
        private const float CONNECTION_WIDTH = 1.1f;
        private static readonly Color PRIMARY_COLOR = Color.FromArgb(255, 215, 100);
        private static readonly Color GLOW_COLOR = Color.FromArgb(255, 230, 150);
        #endregion

        private readonly Guna2Panel _panel;
        private readonly List<Particle> _particles = new List<Particle>();
        private readonly List<Connection> _connections = new List<Connection>();
        private readonly Random _rand = new Random();
        private Bitmap _buffer;
        private readonly System.Windows.Forms.Timer _timer;


        public ParticleSystem(Guna2Panel panel)
        {
            _panel = panel ?? throw new ArgumentNullException(nameof(panel));

            // Inizializzazione
            SetupBuffer();
            CreateParticles();

            // Configurazione timer
            _timer = new System.Windows.Forms.Timer { Interval = 15 };
            _timer.Tick += (s, e) => Update();

            // Collegamento eventi
            _panel.Paint += Render;
            _panel.SizeChanged += (s, e) => SetupBuffer();
            _panel.VisibleChanged += (s, e) => _timer.Enabled = _panel.Visible;

            _timer.Start();
        }

        private void SetupBuffer()
        {
            _buffer?.Dispose();
            if (_panel.Width > 0 && _panel.Height > 0)
                _buffer = new Bitmap(_panel.Width, _panel.Height);
        }

        private void CreateParticles()
        {
            var speeds = new List<(int, int)>
            {
                (1,1), (1,0), (1,-1), (0,1), (0,-1), (-1,1), (-1,0), (-1,-1),
                (2,1), (1,2), (-2,1), (1,-2)
            };

            for (int i = 0; i < PARTICLE_COUNT; i++)
            {
                _particles.Add(new Particle
                {
                    Position = new Point(
                        _rand.Next(30, Math.Max(30, _panel.Width - 30)),
                        _rand.Next(30, Math.Max(30, _panel.Height - 30))),
                    Speed = speeds[_rand.Next(speeds.Count)],
                    Size = _rand.Next(4, 7),
                    Color = PRIMARY_COLOR,
                    GlowColor = GLOW_COLOR
                });
            }
        }

        private void Update()
        {
            if (!_panel.Visible) return;

            MoveParticles();
            UpdateConnections();
            _panel.Invalidate();
        }

        private void MoveParticles()
        {
            for (int i = 0; i < _particles.Count; i++)
            {
                var p = _particles[i];
                p.Position = new Point(
                    p.Position.X + p.Speed.Item1,
                    p.Position.Y + p.Speed.Item2);

                // Gestione bordi con margine
                if (p.Position.X < 20 || p.Position.X > _panel.Width - 20)
                    p.Speed.Item1 *= -1;

                if (p.Position.Y < 20 || p.Position.Y > _panel.Height - 20)
                    p.Speed.Item2 *= -1;

                _particles[i] = p;
            }
        }

        private void UpdateConnections()
        {
            _connections.Clear();

            for (int i = 0; i < _particles.Count; i++)
            {
                for (int j = i + 1; j < _particles.Count; j++)
                {
                    var dist = Distance(_particles[i].Position, _particles[j].Position);
                    if (dist <= MAX_CONNECTION_DISTANCE)
                    {
                        int alpha = (int)Math.Max(
                            CONNECTION_ALPHA_MIN,
                            CONNECTION_ALPHA_MAX * (1 - dist / MAX_CONNECTION_DISTANCE));

                        _connections.Add(new Connection
                        {
                            Start = _particles[i].Position,
                            End = _particles[j].Position,
                            Alpha = alpha
                        });
                    }
                }
            }
        }

        private void Render(object sender, PaintEventArgs e)
        {
            if (_panel.Width <= 0 || _panel.Height <= 0 || !_panel.Visible)
                return;

            try
            {
                using (var g = Graphics.FromImage(_buffer))
                {
                    g.Clear(Color.Transparent);
                    g.SmoothingMode = SmoothingMode.HighQuality;

                    // Disegna connessioni
                    foreach (var conn in _connections)
                    {
                        using (var pen = new Pen(Color.FromArgb(conn.Alpha, 255, 255, 255), CONNECTION_WIDTH))
                            g.DrawLine(pen, conn.Start, conn.End);
                    }

                    // Disegna particelle con glow
                    foreach (var p in _particles)
                    {
                        // Glow esterno (3 livelli)
                        for (int i = 3; i > 0; i--)
                        {
                            int size = p.Size + i * 4;
                            using (var brush = new SolidBrush(Color.FromArgb(40 - i * 10, p.GlowColor)))
                                g.FillEllipse(brush, p.Position.X - size / 2, p.Position.Y - size / 2, size, size);
                        }

                        // Particella principale
                        using (var brush = new SolidBrush(p.Color))
                            g.FillEllipse(brush, p.Position.X - p.Size / 2, p.Position.Y - p.Size / 2, p.Size, p.Size);
                    }
                }

                e.Graphics.DrawImage(_buffer, Point.Empty);
            }
            catch { /* Ignora errori di rendering */ }
        }

        private static float Distance(Point a, Point b) =>
            (float)Math.Sqrt(Math.Pow(b.X - a.X, 2) + Math.Pow(b.Y - a.Y, 2));

        public void Dispose()
        {
            _timer?.Stop();
            _timer?.Dispose();
            _panel.Paint -= Render;
            _buffer?.Dispose();
        }

        private struct Particle
        {
            public Point Position;
            public (int, int) Speed;
            public int Size;
            public Color Color;
            public Color GlowColor;
        }

        private struct Connection
        {
            public Point Start;
            public Point End;
            public int Alpha;
        }
    }
}