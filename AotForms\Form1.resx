﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="guna2BorderlessForm1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="guna2Button4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAZ8SURBVHhe7Z3N
        jtxUEIXzAgQBYgEMETsQWyTeAQmiSAgeIn+8wmT+Z8kOBGLBLhEobALhSZhswwYhiEhAsIAIzkUno3K5
        7Lbvta+7J+eTPkVyn6q2XD3d9o1n+pwQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBDi
        aWB7e/vCjRs3ruHfu/AE/gH/lbOajnE61umYX93b23uV46gHnvQVPPmn8B8Y7aSs52N4C77G8cwLnugS
        /B1GOyOX8xG8yDHNA97uP8KTpFdctANyedNsrnNc04LG6Sdfw19/04ymfSdAwy2ot/3NMX0cvMzxlYNm
        X5jmcjP8jOMrA40uQJ3tb55pZlscYz68zo+ewHofufePj4+fYZmYiXSMcbzT+VhaC4hmcSpmcoVl+aDR
        d76x8z58nnFRicPDw+d47KOZPPEO4/mgyT3XtGH6yWdUVAbH/oNoJsYTRvNBk3RGGTX/X73tLweO/3k/
        D+cjRvMJmjZkrJe0Xo3sVzB6MaXLy7s7OztvMd5i5nrv7PuDx9+G3zPr61PP27u7u68z3ourbclYPlFT
        K2Od8GD96usC/4RvsOyUivXeWfYH296EfzHT54P0XCzrJKhryFg+UVMrY50gk35SwtrAr1l2CrbVrPfO
        sT/fuEyft1jWSVDTkLF8oqZWxjpBZsjb7hN/Ytkp2Faz3jvH/vzsMn0+ZFknQU1DxvKJmloZ6wQZvQAM
        2DbmBfAbyzoJahoylk/U1MpYJ8jc9jU9Rm+ZNeu9c+zPmI+AmyzrJKhpyFg+UVMrY52ks1nkHvi6wPCk
        qWK9d5b9wbahJ4G/wJVLua6mJWP5RE2tjPXCM+d018pDW0uHXnbNVe+dfX/weN9lYOp5Ew5axzd1oYzl
        EzW1MiYWIpqJlbF8oqZWxsRCRDOxMpZP1NTKmFiIaCZWxvKJmloZEwsRzcTKWD5RUytjg0C+7+THW3oy
        5d34fhGuR0vG8omaWhlbCbJDL3+8pZdT3o3s14WrbclYPlFTK2MrQXbMAoi3dEHFu3H9ughqGzKWT9TU
        ythKkB2zBOotXVL1bly/LoLahozlEzW1MrYSZPUCGO6ZfAHoI2C4Z/IjQCeBwzybJ4EJ5HUZ2O3KfhGu
        R0vG8omaWhkTCxHNxMpYPlFTK2NiIaKZWBnLJ2pqZUwsRDQTK2P5RE2tjImFiGZiZSyfqKmVsV6Q6zsZ
        Kj2ZWlnPGzi67uP3lu5Peo7e+/rxeFG9xdW2ZCyfqKmVsU6QGXo5VHo5FdZz+JP9XgC2Dd2f8L5+bC+q
        9wR1DRnLJ2pqZawTZMYsiJQuqET1U/9ewJj9ad3Xj21F9Z6gpiFj+URNrYx1gsyYJdHSJdWofurbwsfs
        T+u+fmwrqvcENQ0ZyydqamWsE2Se5hdA675+bCuq9wQ1DRnLJ2pqZawTZJb+CJj69wLG7E/rvn5sK6r3
        BDUNGcsnamplrBNkFj0JnPr3ArBt6P6E9/VjW1G9x9W0ZCyfqKmVsV6QW4fLwMl+LwCP9+3Pyvv68VhR
        vcXUhTKWT9TUyphYiGgmVsbyiZpaGRMLEc3Eylg+UVMrY2IhoplYGcsnamplTCxENBMrY/lETa2MjQJ1
        RWvheLyvvrZV1/49rldLxvKJmloZGwxqitbCsX1ofW2rrP17gj4NGcsnamplbDCoKVoLx7Yx9bWdfe3f
        E/RoyFg+UVMrY4NBTdFaOLaNqa/t7Gv/nqBHQ8byiZpaGRsMamqupdd29rV/T9CjIWP5RE2tjA0GNTXX
        0ms7+9q/J+jRkLF8oqZWxgaDmlpr6bWtsvbvcT1aMpZP1NTK2ChQN+daem2rrv17TJ9QxvJBk97/T9cf
        i16Oo6OjZ6OZGCf5Y9G9fy4eXmJUVAbH/kM3C+8kfy5+1RdGnKQvL2BcVOLg4OAFHPsf3Sy8dxjPB02u
        uqaR6Stj0pcXnGeZmIl0jGH6yV81/ORlluXDmyn0pVGb598w68SyBRp9bhrLzfATjq8cvAukL4suubtW
        1vXh/v7+SxzfNOAz/h001kfB+vsYs3qPY5sWNL+ensA8mVwv0/CvcVzzgCe5CPVxsH6mFcV3OaZ5wTXo
        i3iyj2E604x2RtYzvSN/Ofln/hDwxFt4y7mCf7+FP8B1WJs/66ZjnI71HXgZTnOpJ4QQQgghhBBCCCGE
        EEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhFhrzp37D1W9eeyDwb68AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="guna2Button3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAZ8SURBVHhe7Z3N
        jtxUEIXzAgQBYgEMETsQWyTeAQmiSAgeIn+8wmT+Z8kOBGLBLhEobALhSZhswwYhiEhAsIAIzkUno3K5
        7Lbvta+7J+eTPkVyn6q2XD3d9o1n+pwQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBDi
        aWB7e/vCjRs3ruHfu/AE/gH/lbOajnE61umYX93b23uV46gHnvQVPPmn8B8Y7aSs52N4C77G8cwLnugS
        /B1GOyOX8xG8yDHNA97uP8KTpFdctANyedNsrnNc04LG6Sdfw19/04ymfSdAwy2ot/3NMX0cvMzxlYNm
        X5jmcjP8jOMrA40uQJ3tb55pZlscYz68zo+ewHofufePj4+fYZmYiXSMcbzT+VhaC4hmcSpmcoVl+aDR
        d76x8z58nnFRicPDw+d47KOZPPEO4/mgyT3XtGH6yWdUVAbH/oNoJsYTRvNBk3RGGTX/X73tLweO/3k/
        D+cjRvMJmjZkrJe0Xo3sVzB6MaXLy7s7OztvMd5i5nrv7PuDx9+G3zPr61PP27u7u68z3ourbclYPlFT
        K2Od8GD96usC/4RvsOyUivXeWfYH296EfzHT54P0XCzrJKhryFg+UVMrY50gk35SwtrAr1l2CrbVrPfO
        sT/fuEyft1jWSVDTkLF8oqZWxjpBZsjb7hN/Ytkp2Faz3jvH/vzsMn0+ZFknQU1DxvKJmloZ6wQZvQAM
        2DbmBfAbyzoJahoylk/U1MpYJ8jc9jU9Rm+ZNeu9c+zPmI+AmyzrJKhpyFg+UVMrY52ks1nkHvi6wPCk
        qWK9d5b9wbahJ4G/wJVLua6mJWP5RE2tjPXCM+d018pDW0uHXnbNVe+dfX/weN9lYOp5Ew5axzd1oYzl
        EzW1MiYWIpqJlbF8oqZWxsRCRDOxMpZP1NTKmFiIaCZWxvKJmloZEwsRzcTKWD5RUytjg0C+7+THW3oy
        5d34fhGuR0vG8omaWhlbCbJDL3+8pZdT3o3s14WrbclYPlFTK2MrQXbMAoi3dEHFu3H9ughqGzKWT9TU
        ythKkB2zBOotXVL1bly/LoLahozlEzW1MrYSZPUCGO6ZfAHoI2C4Z/IjQCeBwzybJ4EJ5HUZ2O3KfhGu
        R0vG8omaWhkTCxHNxMpYPlFTK2NiIaKZWBnLJ2pqZUwsRDQTK2P5RE2tjImFiGZiZSyfqKmVsV6Q6zsZ
        Kj2ZWlnPGzi67uP3lu5Peo7e+/rxeFG9xdW2ZCyfqKmVsU6QGXo5VHo5FdZz+JP9XgC2Dd2f8L5+bC+q
        9wR1DRnLJ2pqZawTZMYsiJQuqET1U/9ewJj9ad3Xj21F9Z6gpiFj+URNrYx1gsyYJdHSJdWofurbwsfs
        T+u+fmwrqvcENQ0ZyydqamWsE2Se5hdA675+bCuq9wQ1DRnLJ2pqZawTZJb+CJj69wLG7E/rvn5sK6r3
        BDUNGcsnamplrBNkFj0JnPr3ArBt6P6E9/VjW1G9x9W0ZCyfqKmVsV6QW4fLwMl+LwCP9+3Pyvv68VhR
        vcXUhTKWT9TUyphYiGgmVsbyiZpaGRMLEc3Eylg+UVMrY2IhoplYGcsnamplTCxENBMrY/lETa2MjQJ1
        RWvheLyvvrZV1/49rldLxvKJmloZGwxqitbCsX1ofW2rrP17gj4NGcsnamplbDCoKVoLx7Yx9bWdfe3f
        E/RoyFg+UVMrY4NBTdFaOLaNqa/t7Gv/nqBHQ8byiZpaGRsMamqupdd29rV/T9CjIWP5RE2tjA0GNTXX
        0ms7+9q/J+jRkLF8oqZWxgaDmlpr6bWtsvbvcT1aMpZP1NTK2ChQN+daem2rrv17TJ9QxvJBk97/T9cf
        i16Oo6OjZ6OZGCf5Y9G9fy4eXmJUVAbH/kM3C+8kfy5+1RdGnKQvL2BcVOLg4OAFHPsf3Sy8dxjPB02u
        uqaR6Stj0pcXnGeZmIl0jGH6yV81/ORlluXDmyn0pVGb598w68SyBRp9bhrLzfATjq8cvAukL4suubtW
        1vXh/v7+SxzfNOAz/h001kfB+vsYs3qPY5sWNL+ensA8mVwv0/CvcVzzgCe5CPVxsH6mFcV3OaZ5wTXo
        i3iyj2E604x2RtYzvSN/Ofln/hDwxFt4y7mCf7+FP8B1WJs/66ZjnI71HXgZTnOpJ4QQQgghhBBCCCGE
        EEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhFhrzp37D1W9eeyDwb68AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="guna2Button5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAZ8SURBVHhe7Z3N
        jtxUEIXzAgQBYgEMETsQWyTeAQmiSAgeIn+8wmT+Z8kOBGLBLhEobALhSZhswwYhiEhAsIAIzkUno3K5
        7Lbvta+7J+eTPkVyn6q2XD3d9o1n+pwQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBDi
        aWB7e/vCjRs3ruHfu/AE/gH/lbOajnE61umYX93b23uV46gHnvQVPPmn8B8Y7aSs52N4C77G8cwLnugS
        /B1GOyOX8xG8yDHNA97uP8KTpFdctANyedNsrnNc04LG6Sdfw19/04ymfSdAwy2ot/3NMX0cvMzxlYNm
        X5jmcjP8jOMrA40uQJ3tb55pZlscYz68zo+ewHofufePj4+fYZmYiXSMcbzT+VhaC4hmcSpmcoVl+aDR
        d76x8z58nnFRicPDw+d47KOZPPEO4/mgyT3XtGH6yWdUVAbH/oNoJsYTRvNBk3RGGTX/X73tLweO/3k/
        D+cjRvMJmjZkrJe0Xo3sVzB6MaXLy7s7OztvMd5i5nrv7PuDx9+G3zPr61PP27u7u68z3ourbclYPlFT
        K2Od8GD96usC/4RvsOyUivXeWfYH296EfzHT54P0XCzrJKhryFg+UVMrY50gk35SwtrAr1l2CrbVrPfO
        sT/fuEyft1jWSVDTkLF8oqZWxjpBZsjb7hN/Ytkp2Faz3jvH/vzsMn0+ZFknQU1DxvKJmloZ6wQZvQAM
        2DbmBfAbyzoJahoylk/U1MpYJ8jc9jU9Rm+ZNeu9c+zPmI+AmyzrJKhpyFg+UVMrY52ks1nkHvi6wPCk
        qWK9d5b9wbahJ4G/wJVLua6mJWP5RE2tjPXCM+d018pDW0uHXnbNVe+dfX/weN9lYOp5Ew5axzd1oYzl
        EzW1MiYWIpqJlbF8oqZWxsRCRDOxMpZP1NTKmFiIaCZWxvKJmloZEwsRzcTKWD5RUytjg0C+7+THW3oy
        5d34fhGuR0vG8omaWhlbCbJDL3+8pZdT3o3s14WrbclYPlFTK2MrQXbMAoi3dEHFu3H9ughqGzKWT9TU
        ythKkB2zBOotXVL1bly/LoLahozlEzW1MrYSZPUCGO6ZfAHoI2C4Z/IjQCeBwzybJ4EJ5HUZ2O3KfhGu
        R0vG8omaWhkTCxHNxMpYPlFTK2NiIaKZWBnLJ2pqZUwsRDQTK2P5RE2tjImFiGZiZSyfqKmVsV6Q6zsZ
        Kj2ZWlnPGzi67uP3lu5Peo7e+/rxeFG9xdW2ZCyfqKmVsU6QGXo5VHo5FdZz+JP9XgC2Dd2f8L5+bC+q
        9wR1DRnLJ2pqZawTZMYsiJQuqET1U/9ewJj9ad3Xj21F9Z6gpiFj+URNrYx1gsyYJdHSJdWofurbwsfs
        T+u+fmwrqvcENQ0ZyydqamWsE2Se5hdA675+bCuq9wQ1DRnLJ2pqZawTZJb+CJj69wLG7E/rvn5sK6r3
        BDUNGcsnamplrBNkFj0JnPr3ArBt6P6E9/VjW1G9x9W0ZCyfqKmVsV6QW4fLwMl+LwCP9+3Pyvv68VhR
        vcXUhTKWT9TUyphYiGgmVsbyiZpaGRMLEc3Eylg+UVMrY2IhoplYGcsnamplTCxENBMrY/lETa2MjQJ1
        RWvheLyvvrZV1/49rldLxvKJmloZGwxqitbCsX1ofW2rrP17gj4NGcsnamplbDCoKVoLx7Yx9bWdfe3f
        E/RoyFg+UVMrY4NBTdFaOLaNqa/t7Gv/nqBHQ8byiZpaGRsMamqupdd29rV/T9CjIWP5RE2tjA0GNTXX
        0ms7+9q/J+jRkLF8oqZWxgaDmlpr6bWtsvbvcT1aMpZP1NTK2ChQN+daem2rrv17TJ9QxvJBk97/T9cf
        i16Oo6OjZ6OZGCf5Y9G9fy4eXmJUVAbH/kM3C+8kfy5+1RdGnKQvL2BcVOLg4OAFHPsf3Sy8dxjPB02u
        uqaR6Stj0pcXnGeZmIl0jGH6yV81/ORlluXDmyn0pVGb598w68SyBRp9bhrLzfATjq8cvAukL4suubtW
        1vXh/v7+SxzfNOAz/h001kfB+vsYs3qPY5sWNL+ensA8mVwv0/CvcVzzgCe5CPVxsH6mFcV3OaZ5wTXo
        i3iyj2E604x2RtYzvSN/Ofln/hDwxFt4y7mCf7+FP8B1WJs/66ZjnI71HXgZTnOpJ4QQQgghhBBCCCGE
        EEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhFhrzp37D1W9eeyDwb68AAAAAElFTkSuQmCC
</value>
  </data>
</root>