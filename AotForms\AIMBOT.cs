﻿using AotForms;
using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static Guna.UI2.Native.WinApi;

namespace Client
{
    public partial class AimbotForm : Form
    {
        public AimbotForm()
        {
            InitializeComponent();
        }

        private void guna2ToggleSwitch1_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.AimBot = guna2ToggleSwitch1.Checked;
        }



        private void guna2ToggleSwitch3_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
        }

        private void guna2TrackBar2_Scroll(object sender, ScrollEventArgs e)
        {
            var DELAY = guna2TrackBar2.Value;

            lblDistance.Text = $"({DELAY})";

            Config.aimdelay = DELAY;
        }

      



        private void guna2ToggleSwitch6_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.AIMFF = guna2ToggleSwitch6.Checked;
        }

        private void guna2TrackBar1_Scroll(object sender, ScrollEventArgs e)
        {

            var fovSize = guna2TrackBar1.Value;
            Config.AimBotFov = fovSize;
            aimfovtxt.Text = $"FOV : {fovSize}";
        }

        private void guna2TrackBar3_Scroll(object sender, ScrollEventArgs e)
        {

            var distance = guna2TrackBar3.Value;

            rangeaim.Text = $"({distance})";

            Config.AimBotMaxDistance = distance;
        }

        private void AimbotForm_Load(object sender, EventArgs e)
        {

            this.TopMost = false;
        }

        private void guna2PictureBox9_Click(object sender, EventArgs e)
        {

        }

        private void guna2Button1_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button1.FillColor = picker.Color;
                Config.Aimfovcolor = picker.Color;
            }
        }
    }
}
