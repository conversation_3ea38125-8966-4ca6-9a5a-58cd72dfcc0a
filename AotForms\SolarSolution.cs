﻿using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using Guna.UI2.WinForms;

public class SolarSolution
{
    private Guna2Panel _panel;
    private Guna2CircleButton _applyButton;
    private Label _titleLabel;
    private Color _selectedColor = Color.FromArgb(94, 148, 255);
    private Control _targetControl;
    private PictureBox _colorWheel;
    private Bitmap _colorGradientBitmap;
    private bool _isDraggingColor = false;
    private int _selectorX;
    private Guna2CircleButton _colorPreview;

    public SolarSolution(Guna2Panel panel, Control targetControl = null)
    {
        _panel = panel;
        _targetControl = targetControl ?? panel;
        InitializeComponents();
        CreateColorGradient();
    }

    private void InitializeComponents()
    {
        // Configura il panel principale
        _panel.BackColor = Color.FromArgb(30, 30, 46);
        _panel.BorderRadius = 15;
        _panel.FillColor = Color.FromArgb(24, 24, 37);
        _panel.Padding = new Padding(20);

        // Aggiungi il titolo
        _titleLabel = new Label
        {
            Text = "SOLAR SOLUTION",
            Font = new Font("Segoe UI", 12, FontStyle.Bold),
            ForeColor = Color.White,
            AutoSize = true,
            Location = new Point(20, 20)
        };
        _panel.Controls.Add(_titleLabel);

        // Aggiungi il pulsante di applicazione
        _applyButton = new Guna2CircleButton
        {
            Size = new Size(60, 60),
            Location = new Point(_panel.Width / 2 - 30, 280),
            FillColor = Color.FromArgb(46, 46, 70),
            Text = "APPLY",
            ForeColor = Color.White,
            Font = new Font("Segoe UI", 8, FontStyle.Bold),
            Animated = true
        };
        _applyButton.Click += ApplyButton_Click;
        _panel.Controls.Add(_applyButton);

        // Aggiungi preview del colore
        _colorPreview = new Guna2CircleButton
        {
            Size = new Size(40, 40),
            Location = new Point(_panel.Width / 2 - 20, 220),
            FillColor = _selectedColor,
            Enabled = false
        };
        _panel.Controls.Add(_colorPreview);
    }

    private void CreateColorGradient()
    {
        // Crea il PictureBox per il gradiente
        _colorWheel = new PictureBox
        {
            Size = new Size(_panel.Width - 40, 40),
            Location = new Point(20, 80),
            BackColor = Color.Transparent
        };

        // Crea il bitmap del gradiente
        _colorGradientBitmap = new Bitmap(_colorWheel.Width, _colorWheel.Height);
        using (Graphics g = Graphics.FromImage(_colorGradientBitmap))
        {
            Rectangle rect = new Rectangle(0, 0, _colorGradientBitmap.Width, _colorGradientBitmap.Height);
            using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.Red, Color.Violet, LinearGradientMode.Horizontal))
            {
                ColorBlend colorBlend = new ColorBlend
                {
                    Colors = new Color[] {
                        Color.Red,
                        Color.Orange,
                        Color.Yellow,
                        Color.Green,
                        Color.Cyan,
                        Color.Blue,
                        Color.Violet
                    },
                    Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                };
                brush.InterpolationColors = colorBlend;
                g.FillRectangle(brush, rect);
            }
        }

        _colorWheel.Image = _colorGradientBitmap;
        _colorWheel.Cursor = Cursors.Hand;
        _colorWheel.MouseDown += ColorWheel_MouseDown;
        _colorWheel.MouseMove += ColorWheel_MouseMove;
        _colorWheel.MouseUp += ColorWheel_MouseUp;
        _colorWheel.Paint += ColorWheel_Paint;
        _panel.Controls.Add(_colorWheel);

        // Posizione iniziale del selettore
        _selectorX = _colorWheel.Width / 2;
        UpdateColor(_selectorX);
    }

    private void ColorWheel_Paint(object sender, PaintEventArgs e)
    {
        DrawSelector(e.Graphics, _selectorX, _colorWheel.Height / 2);
    }

    private void ColorWheel_MouseDown(object sender, MouseEventArgs e)
    {
        _isDraggingColor = true;
        UpdateColor(e.X);
    }

    private void ColorWheel_MouseMove(object sender, MouseEventArgs e)
    {
        if (_isDraggingColor && e.X >= 0 && e.X < _colorWheel.Width)
        {
            UpdateColor(e.X);
        }
    }

    private void ColorWheel_MouseUp(object sender, MouseEventArgs e)
    {
        _isDraggingColor = false;
    }

    private void DrawSelector(Graphics g, int x, int y)
    {
        int circleSize = 12;
        using (Pen borderPen = new Pen(Color.White, 2))
        using (Pen innerPen = new Pen(Color.Black, 1))
        {
            // Cerchio esterno bianco
            g.DrawEllipse(borderPen, x - circleSize / 2, y - circleSize / 2, circleSize, circleSize);

            // Cerchio interno nero
            g.DrawEllipse(innerPen, x - (circleSize / 2 - 1), y - (circleSize / 2 - 1), circleSize - 2, circleSize - 2);
        }
    }

    private void UpdateColor(int x)
    {
        _selectorX = Math.Max(0, Math.Min(x, _colorWheel.Width - 1));

        if (_colorGradientBitmap != null)
        {
            _selectedColor = _colorGradientBitmap.GetPixel(_selectorX, _colorWheel.Height / 2);
            _colorPreview.FillColor = _selectedColor;
            _colorWheel.Invalidate();
        }
    }

    private void ApplyButton_Click(object sender, EventArgs e)
    {
        if (_targetControl is Guna2Panel panel)
        {
            panel.FillColor = _selectedColor;
        }
        else if (_targetControl is Control control)
        {
            control.BackColor = _selectedColor;
        }

        // Aggiungi effetto visivo quando si applica il colore
        _applyButton.FillColor = _selectedColor;

        System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer { Interval = 300 };
        timer.Tick += (s, args) =>
        {
            _applyButton.FillColor = Color.FromArgb(46, 46, 70);
            timer.Stop();
            timer.Dispose();
        };
        timer.Start();
    }


    public void SetTargetControl(Control target)
    {
        _targetControl = target;
    }

    public void SetTitle(string title)
    {
        _titleLabel.Text = title;
    }
}