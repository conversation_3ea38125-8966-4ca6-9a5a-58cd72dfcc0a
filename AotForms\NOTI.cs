﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AotForms
{
    public partial class NOTI : Form
    {
        
        public NOTI()
        {
            InitializeComponent();
           
        }

        private void AKARD_Load(object sender, EventArgs e)
        {

        }

      
        public enum enmAction
        {
            wait,
            start,
            close
        }

        public enum enmType
        {
            Applying,
            Applied,
            Error,
            Info
        }
        private NOTI.enmAction action;

        private int x, y;



        public void showAlert(string msg, enmType type)
        {
            this.Opacity = 0.0;
            this.StartPosition = FormStartPosition.Manual;
            string fname;

            for (int i = 1; i < 10; i++)
            {
                fname = "alert" + i.ToString();
                NOTI frm = (NOTI)Application.OpenForms[fname];

                if (frm == null)
                {
                    this.Name = fname;
                    this.x = Screen.PrimaryScreen.WorkingArea.Width - this.Width + 15;
                    this.y = Screen.PrimaryScreen.WorkingArea.Height - this.Height * i - 5 * i;
                    this.Location = new Point(this.x, this.y);
                    break;

                }
            }
            this.x = Screen.PrimaryScreen.WorkingArea.Width - base.Width - 5;

            switch (type)
            {
                case enmType.Applying:
                    GH.ForeColor = Color.Orange;
                    this.GH.Text = msg;
                    break;
                case enmType.Applied:
                    GH.ForeColor = Color.DarkGreen;
                    this.GH.Text = msg;
                    break;
                case enmType.Error:
                    GH.ForeColor = Color.Red;
                    this.GH.Text = msg;
                    break;
            }

            this.Show();
            this.action = enmAction.start;
            this.timer1.Interval = 1;
            this.timer1.Start();
        }


        private void timer1_Tick(object sender, EventArgs e)
        {
            {
                switch (this.action)
                {
                    case enmAction.wait:
                        timer1.Interval = 1000;
                        action = enmAction.close;
                        break;
                    case AotForms.NOTI.enmAction.start:
                        this.timer1.Interval = 1;
                        this.Opacity += 0.1;
                        if (this.x < this.Location.X)
                        {
                            this.Left--;
                        }
                        else
                        {
                            if (this.Opacity == 1.0)
                            {
                                action = AotForms.NOTI.enmAction.wait;
                            }
                        }
                        break;
                    case enmAction.close:
                        timer1.Interval = 1;
                        this.Opacity -= 0.1;

                        this.Left -= 3;
                        if (base.Opacity == 0.0)
                        {
                            base.Close();
                        }
                        break;
                }
            }
        }
    }
}