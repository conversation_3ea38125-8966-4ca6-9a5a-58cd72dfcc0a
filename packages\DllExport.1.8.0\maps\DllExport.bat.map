oo=dpnx0
op=args
oq=ddargs
or=esc
os=E_CARET
ot=dxpVersion
ou=dxpName
ov=tWizard
ow=dxpPackages
ox=pkgSrv
oy=buildInfoFile
oz=fManager
oa=dxpDebug
ob=buildInfo
oc=gMsbPath
od=pkgLink
oe=peVcmd
of=kForce
og=mgrUp
oh=proxy
oi=xmgrtest
oj=khMSBuild
ok=EXIT_CODE
ol=idx
om=key
on=reqPkg
o0=wzTarget
o1=_gntC
o2=argsWz
o3=_hmsbC
o4=klen
o5=found
o6=kargs
o7=_ic
o8=dmsg
o9=ins
po=_rt
pp=arg
pq=amax
pr=action
ps=usage
pt=commands
pu=endpoint
pv=loopargs
pw=continue
px=eval
py=ktoolinit
pz=dbgprint
pa=trim
pb=invokeCore
pc=hMSBuild
pd=rtrim
pe=ltrim
pf=_trim
pg=_tpos
