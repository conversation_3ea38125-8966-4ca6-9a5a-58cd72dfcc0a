//
// Auto-generated by dnne-gen
//
// .NET Assembly: shg
//

//
// Declare exported functions
//
#ifndef __DNNE_GENERATED_HEADER_SHG__
#define __DNNE_GENERATED_HEADER_SHG__

#include <stddef.h>
#include <stdint.h>
#ifdef DNNE_COMPILE_AS_SOURCE
    #include <dnne.h>
#else
    // When used as a header file, the assumption is
    // dnne.h will be next to this file.
    #include "dnne.h"
#endif // !DNNE_COMPILE_AS_SOURCE

#if (defined(DNNE_WINDOWS))
// Computed from AotForms.Program.Load
DNNE_EXTERN_C DNNE_API void DNNE_CALLTYPE Load(intptr_t pVM);
#endif // (DNNE_WINDOWS)

#endif // __DNNE_GENERATED_HEADER_SHG__

//
// Define exported functions
//
#ifdef DNNE_COMPILE_AS_SOURCE

#ifdef DNNE_WINDOWS
    #ifdef _WCHAR_T_DEFINED
        typedef wchar_t char_t;
    #else
        typedef unsigned short char_t;
    #endif
#else
    typedef char char_t;
#endif

//
// Forward declarations
//

extern void* get_callable_managed_function(
    const char_t* dotnet_type,
    const char_t* dotnet_type_method,
    const char_t* dotnet_delegate_type);

extern void* get_fast_callable_managed_function(
    const char_t* dotnet_type,
    const char_t* dotnet_type_method);

//
// String constants
//

#ifdef DNNE_TARGET_NET_FRAMEWORK
    static const char_t* t1_name = DNNE_STR("AotForms.Program");
#else
    static const char_t* t1_name = DNNE_STR("AotForms.Program, shg");
#endif // !DNNE_TARGET_NET_FRAMEWORK


//
// Exports
//

#if (defined(DNNE_WINDOWS))
// Computed from AotForms.Program.Load
static void (DNNE_CALLTYPE* Load_ptr)(intptr_t pVM);
DNNE_EXTERN_C DNNE_API void DNNE_CALLTYPE Load(intptr_t pVM)
{
    if (Load_ptr == NULL)
    {
        const char_t* methodName = DNNE_STR("Load");
        Load_ptr = (void(DNNE_CALLTYPE*)(intptr_t pVM))get_fast_callable_managed_function(t1_name, methodName);
    }
    Load_ptr(pVM);
}
#endif // (DNNE_WINDOWS)

#endif // DNNE_COMPILE_AS_SOURCE
