using AotForms;
using ImGuiNET;
using System.Diagnostics;
using System.Numerics;
using System.Runtime.InteropServices;
using static AotForms.WinAPI;

namespace AotForms
{

    internal class ESP : ClickableTransparentOverlay.Overlay
    {
        IntPtr hWnd;
        IntPtr HDPlayer;
        private const short DefaultMaxHealth = 200; // Default maximum health
        protected override unsafe void Render()
        {
            try
            {
                using (PerformanceMonitor.StartMeasurement("ESP_Render"))
                {
                    // تحديث نبضة المراقبة أولاً
                    ProcessMonitor.UpdateESPHeartbeat();

                    // Early exit if no matrix or invalid dimensions
                    if (!Core.HaveMatrix || Core.Width <= 0 || Core.Height <= 0)
                    {
                        Thread.Sleep(PerformanceConfig.TaskDelayOnDisabled);
                        return;
                    }

                    // تحديث معلومات النافذة
                    CreateHandle();

                    // التأكد من صحة الأبعاد بعد التحديث
                    if (Core.Width <= 0 || Core.Height <= 0)
                    {
                        return;
                    }

                    var drawList = ImGui.GetForegroundDrawList();
                    if (drawList.NativePtr == IntPtr.Zero)
                    {
                        ErrorHandler.LogWarning("Invalid draw list pointer");
                        return;
                    }

                    // رسم دائرة الهدف إذا كانت مفعلة
                    if (Config.Aimfovc && Config.Aimfov > 0)
                    {
                        try
                        {
                            DrawSmoothCircle(Config.Aimfov, ColorToUint32(Config.Aimfovcolor), 1.0f);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandler.LogError("Error drawing aim FOV circle", ex);
                        }
                    }

                    // Handle window styles
                    string windowName = "Overlay";
                    hWnd = FindWindow(null, windowName);
                    HDPlayer = FindWindow("BlueStacksApp", null);

                    if (hWnd != IntPtr.Zero)
                    {
                        long extendedStyle = GetWindowLong(hWnd, GWL_EXSTYLE);
                        SetWindowLong(hWnd, GWL_EXSTYLE, (extendedStyle | WS_EX_TOOLWINDOW) & ~WS_EX_APPWINDOW);
                    }
                    else
                    {
                        Console.WriteLine("The window was not found.");
                    }
                    // Process entities with performance monitoring
                    ProcessEntities(drawList);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("ESP Render error", ex);
            }
        }

        private void ProcessEntities(ImDrawListPtr drawList)
        {
            try
            {
                // التحقق من صحة المدخلات
                if (drawList.NativePtr == IntPtr.Zero)
                {
                    ErrorHandler.LogWarning("Invalid draw list in ProcessEntities");
                    return;
                }

                // Create snapshot to avoid collection modification issues
                var entitiesSnapshot = Core.Entities?.Values?.ToArray();
                if (entitiesSnapshot == null || entitiesSnapshot.Length == 0)
                {
                    return;
                }

                var processedCount = 0;
                var maxEntitiesPerFrame = PerformanceConfig.MaxEntitiesPerFrame;
                var validEntitiesCount = 0;

                foreach (var entity in entitiesSnapshot)
                {
                    if (processedCount >= maxEntitiesPerFrame) break;

                    // فحص صحة الكائن
                    if (entity == null) continue;
                    if (entity.IsDead == true || entity.IsKnown != true) continue;

                    // فحص صحة المواقع
                    if (entity.Head == Vector3.Zero || Core.LocalMainCamera == Vector3.Zero) continue;

                    // فحص المسافة
                    var dist = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                    if (dist <= 0 || dist > Config.espran || float.IsNaN(dist) || float.IsInfinity(dist)) continue;

                    // معالجة الكائن
                    try
                    {
                        ProcessSingleEntity(entity, drawList);
                        processedCount++;
                        validEntitiesCount++;
                    }
                    catch (Exception entityEx)
                    {
                        ErrorHandler.LogError($"Error processing entity at distance {dist}", entityEx);
                        continue;
                    }
                }

                // تسجيل إحصائيات للمراقبة
                if (validEntitiesCount == 0 && entitiesSnapshot.Length > 0)
                {
                    ErrorHandler.LogWarning($"No valid entities processed from {entitiesSnapshot.Length} total entities");
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("ProcessEntities error", ex);
            }
        }

        private void ProcessSingleEntity(Entity entity, ImDrawListPtr drawList)
        {
            try
            {
                // التحقق من صحة الكائن والمصفوفة
                if (entity == null || Core.CameraMatrix == default(Matrix4x4))
                {
                    return;
                }

                // تحويل المواقع إلى إحداثيات الشاشة
                var headScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                var bottomScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                // فحص صحة المواقع على الشاشة
                if (!IsValidScreenPosition(headScreenPos) || !IsValidScreenPosition(bottomScreenPos))
                {
                    return;
                }

                // فحص إذا كان الكائن داخل حدود الشاشة
                if (!IsOnScreen(headScreenPos) && !IsOnScreen(bottomScreenPos))
                {
                    return;
                }

                float CornerHeight = Math.Abs(headScreenPos.Y - bottomScreenPos.Y);
                float CornerWidth = (float)(CornerHeight * 0.65);

                // رسم خطوط ESP
                if (Config.ESPLine)
                {
                    try
                    {
                        Vector2 lineStartPos = GetLineStartPosition();

                        // التحقق من صحة نقطة البداية
                        if (IsValidScreenPosition(lineStartPos))
                        {
                            // رسم الخط إذا لم يكن الكائن مصاب
                            if (!entity.IsKnocked)
                            {
                                var lineDrawList = ImGui.GetBackgroundDrawList();
                                if (lineDrawList.NativePtr != IntPtr.Zero)
                                {
                                    lineDrawList.AddLine(lineStartPos, headScreenPos, ColorToUint32(Config.ESPLineColor), 1f);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandler.LogError("Error drawing ESP line", ex);
                    }
                }









                // رسم صناديق ESP
                float boxX = headScreenPos.X - (CornerWidth / 2);
                float boxY = headScreenPos.Y;

                // التحقق من صحة أبعاد الصندوق
                if (CornerWidth > 0 && CornerHeight > 0 && !float.IsNaN(CornerWidth) && !float.IsNaN(CornerHeight))
                {
                    if (Config.ESPBox)
                    {
                        try
                        {
                            uint boxColor = ColorToUint32(Config.ESPBoxColor);
                            DrawCorneredBox(boxX, boxY, CornerWidth, CornerHeight, boxColor, 1f);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandler.LogError("Error drawing ESP box", ex);
                        }
                    }

                    if (Config.ESPFillBox)
                    {
                        try
                        {
                            uint boxColor = ColorToUint32(Color.FromArgb((int)(0.2f * 255), Config.ESPFillBoxColor));
                            DrawFilledBox(boxX, boxY, CornerWidth, CornerHeight, boxColor);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandler.LogError("Error drawing ESP filled box", ex);
                        }
                    }

                    if (Config.ESPBox2)
                    {
                        try
                        {
                            uint boxColor = ColorToUint32(Config.ESPBoxColor);
                            Draw3dBox(boxX, boxY, CornerWidth, CornerHeight, boxColor, 1f);
                        }
                        catch (Exception ex)
                        {
                            ErrorHandler.LogError("Error drawing ESP 3D box", ex);
                        }
                    }
                }
                // رسم الأسماء والمعلومات
                Vector2 fixedNameSize = new Vector2(95, 16);

                // التأكد من وجود اسم للكائن
                if (string.IsNullOrEmpty(entity.Name))
                    entity.Name = "Bot";

                // التحقق من أن الرأس داخل حدود الشاشة
                if (IsOnScreen(headScreenPos))
                {
                    try
                    {
                        Vector2 namePos = new Vector2(headScreenPos.X - fixedNameSize.X / 2, headScreenPos.Y - fixedNameSize.Y - 15);

                        // التحقق من صحة موقع الاسم
                        if (IsValidScreenPosition(namePos))
                        {
                            Vector2 textSizeName = ImGui.CalcTextSize(entity.Name);
                            float distance = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                            string distanceText = $" ({MathF.Round(distance)}m)";
                            Vector2 textSizeDistance = ImGui.CalcTextSize(distanceText);

                            Vector2 textPosName = new Vector2(namePos.X + 5, namePos.Y + (fixedNameSize.Y - textSizeName.Y) / 2);
                            Vector2 textPosDistance = new Vector2(namePos.X + fixedNameSize.X - textSizeDistance.X + 5, namePos.Y + (fixedNameSize.Y - textSizeDistance.Y) / 2);



                            // رسم الاسم
                            if (Config.ESPName)
                            {
                                try
                                {
                                    var foregroundDrawList = ImGui.GetForegroundDrawList();
                                    if (foregroundDrawList.NativePtr != IntPtr.Zero)
                                    {
                                        // رسم خلفية الاسم
                                        foregroundDrawList.AddRectFilled(namePos, namePos + fixedNameSize,
                                            ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.7f)), 3f);

                                        // رسم النص
                                        foregroundDrawList.AddText(textPosName, ColorToUint32(Config.ESPNameColor), entity.Name);
                                        foregroundDrawList.AddText(textPosDistance, ColorToUint32(Config.ESPNameColor), distanceText);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ErrorHandler.LogError("Error drawing ESP name", ex);
                                }
                            }
                            // رسم شريط الصحة
                            if (Config.ESPHealth && Config.ESPName)
                            {
                                try
                                {
                                    Vector2 barPos = new Vector2(namePos.X, namePos.Y + fixedNameSize.Y);
                                    var healthDrawList = ImGui.GetForegroundDrawList();

                                    if (healthDrawList.NativePtr != IntPtr.Zero)
                                    {
                                        // حساب نسبة الصحة
                                        float healthPercentage = CalculateHealthPercentage(entity.Health);
                                        float barWidth = fixedNameSize.X * healthPercentage;

                                        // تحديد لون الصحة
                                        uint barColor = GetHealthColor(healthPercentage);

                                        // رسم خلفية شريط الصحة
                                        healthDrawList.AddRectFilled(
                                            new Vector2(barPos.X, barPos.Y),
                                            new Vector2(barPos.X + fixedNameSize.X, barPos.Y + 2),
                                            0x90000000);

                                        // رسم شريط الصحة
                                        barColor = entity.IsKnocked ? ColorToUint32(Color.Red) : barColor;
                                        healthDrawList.AddRectFilled(
                                            new Vector2(barPos.X, barPos.Y),
                                            new Vector2(barPos.X + barWidth, barPos.Y + 2),
                                            barColor);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    ErrorHandler.LogError("Error drawing ESP health bar", ex);
                                }
                            }
                            // رسم الهيكل العظمي
                            if (Config.ESPSkeleton)
                            {
                                try
                                {
                                    DrawSkeleton(entity);
                                }
                                catch (Exception ex)
                                {
                                    ErrorHandler.LogError("Error drawing ESP skeleton", ex);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorHandler.LogError("Error in name/health rendering", ex);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("ProcessSingleEntity error", ex);
            }
        }
        public void DrawFilledBox(float X, float Y, float W, float H, uint color)
        {
            var vList = ImGui.GetForegroundDrawList();
            vList.AddRectFilled(new Vector2(X, Y), new Vector2(X + W, Y + H), color);
        }

        public void DrawFilledCircle(float centerY, float radius, int numSegments = 64)
        {
            var vList = ImGui.GetBackgroundDrawList();

            // Set the center of the circle at the middle of the screen horizontally (Core.Width / 2f)

            float centerX = Core.Width / 2f;

            uint colorR = ColorToUint32(Color.FromArgb((int)(1f * 255), 225, 0, 0)); // Red color with full opacity
            uint colorG = ColorToUint32(Color.FromArgb((int)(1f * 255), 0, 255, 0)); // LimeGreen color with full opacity

            // Shadow parameters
            float shadowOffset = 1.5f; // The subtle offset of the shadow from the circle
            uint shadowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(0f, 0f, 0f, 1f)); // Semi-transparent black for a soft shadow

            // Draw shadow (a larger circle slightly offset behind the main one)
            vList.AddCircleFilled(new Vector2(centerX, centerY), radius + shadowOffset, shadowColor, numSegments);

            if (Config.AimBot)
            {
                // Draw main circle
                vList.AddCircleFilled(new Vector2(centerX, centerY), radius, colorR, numSegments);
            }
            else
            {
                // Draw main circle
                vList.AddCircleFilled(new Vector2(centerX, centerY), radius, colorG, numSegments);
            }
        }
        public void DrawCorneredBox(float X, float Y, float W, float H, uint color, float thickness)
        {
            var vList = ImGui.GetForegroundDrawList();

            float lineW = W / 3;
            float lineH = H / 3;

            vList.AddLine(new Vector2(X, Y - thickness / 2), new Vector2(X, Y + lineH), color, thickness);
            vList.AddLine(new Vector2(X - thickness / 2, Y), new Vector2(X + lineW, Y), color, thickness);
            vList.AddLine(new Vector2(X + W - lineW, Y), new Vector2(X + W + thickness / 2, Y), color, thickness);
            vList.AddLine(new Vector2(X + W, Y - thickness / 2), new Vector2(X + W, Y + lineH), color, thickness);
            vList.AddLine(new Vector2(X, Y + H - lineH), new Vector2(X, Y + H + thickness / 2), color, thickness);
            vList.AddLine(new Vector2(X - thickness / 2, Y + H), new Vector2(X + lineW, Y + H), color, thickness);
            vList.AddLine(new Vector2(X + W - lineW, Y + H), new Vector2(X + W + thickness / 2, Y + H), color, thickness);
            vList.AddLine(new Vector2(X + W, Y + H - lineH), new Vector2(X + W, Y + H + thickness / 2), color, thickness);
        }

        public void Draw3dBox(float X, float Y, float W, float H, uint color, float thickness)
        {

            var vList = ImGui.GetForegroundDrawList();

            Vector3[] screentions = new Vector3[]
            {
        new Vector3(X, Y, 0),                // Top-left front
        new Vector3(X, Y + H, 0),            // Bottom-left front
        new Vector3(X + W, Y + H, 0),        // Bottom-right front
        new Vector3(X + W, Y, 0),            // Top-right front
        new Vector3(X, Y, -W),               // Top-left back
        new Vector3(X, Y + H, -W),           // Bottom-left back
        new Vector3(X + W, Y + H, -W),       // Bottom-right back
        new Vector3(X + W, Y, -W)            // Top-right back
            };

            // Draw front face
            vList.AddLine(new Vector2(screentions[0].X, screentions[0].Y), new Vector2(screentions[1].X, screentions[1].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[1].X, screentions[1].Y), new Vector2(screentions[2].X, screentions[2].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[2].X, screentions[2].Y), new Vector2(screentions[3].X, screentions[3].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[3].X, screentions[3].Y), new Vector2(screentions[0].X, screentions[0].Y), color, thickness);

            // Draw back face
            vList.AddLine(new Vector2(screentions[4].X, screentions[4].Y), new Vector2(screentions[5].X, screentions[5].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[5].X, screentions[5].Y), new Vector2(screentions[6].X, screentions[6].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[6].X, screentions[6].Y), new Vector2(screentions[7].X, screentions[7].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[7].X, screentions[7].Y), new Vector2(screentions[4].X, screentions[4].Y), color, thickness);

            // Draw connecting lines
            vList.AddLine(new Vector2(screentions[0].X, screentions[0].Y), new Vector2(screentions[4].X, screentions[4].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[1].X, screentions[1].Y), new Vector2(screentions[5].X, screentions[5].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[2].X, screentions[2].Y), new Vector2(screentions[6].X, screentions[6].Y), color, thickness);
            vList.AddLine(new Vector2(screentions[3].X, screentions[3].Y), new Vector2(screentions[7].X, screentions[7].Y), color, thickness);

        }
        private void DrawSkeleton(Entity entity)
        {
            try
            {
                var drawList = ImGui.GetForegroundDrawList();
                if (drawList.NativePtr == IntPtr.Zero) return;

                uint skeletonColor = ColorToUint32(Config.ESPSkeletonColor);

                // تحويل مواقع العظام إلى إحداثيات الشاشة
                var headScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                var spineScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Spine, Core.Width, Core.Height);
                var rootScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                // التحقق من صحة المواقع
                if (!IsValidScreenPosition(headScreenPos) || !IsValidScreenPosition(spineScreenPos) || !IsValidScreenPosition(rootScreenPos))
                    return;

                // رسم الهيكل الأساسي
                if (IsOnScreen(headScreenPos) && IsOnScreen(spineScreenPos))
                {
                    drawList.AddLine(headScreenPos, spineScreenPos, skeletonColor, 1.5f);
                }

                if (IsOnScreen(spineScreenPos) && IsOnScreen(rootScreenPos))
                {
                    drawList.AddLine(spineScreenPos, rootScreenPos, skeletonColor, 1.5f);
                }

                // رسم دائرة حول الرأس
                float distance = entity.Distance;
                if (distance > 0 && !float.IsNaN(distance) && !float.IsInfinity(distance))
                {
                    float baseRadius = 10.0f;
                    float circleRadius = Math.Max(2f, Math.Min(20f, baseRadius / (distance * 0.1f)));

                    if (IsOnScreen(headScreenPos))
                    {
                        drawList.AddCircle(headScreenPos, circleRadius, skeletonColor, 16);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("DrawSkeleton error", ex);
            }
        }
        private void DrawLine(ImDrawListPtr drawList, Vector2 startPos, Vector2 endPos, uint color)
        {
            if (startPos.X > 0 && startPos.Y > 0 && endPos.X > 0 && endPos.Y > 0)
            {
                drawList.AddLine(startPos, endPos, color, 1.5f); // Adjust thickness as needed
            }
        }

        public void DrawSmoothCircle(float radius, uint color, float thickness, int segments = 64)
        {
            var vList = ImGui.GetForegroundDrawList();
            var io = ImGui.GetIO();
            float centerX = io.DisplaySize.X / 2;
            float centerY = io.DisplaySize.Y / 2;

            vList.AddCircle(new Vector2(centerX, centerY), radius, color, segments, thickness);
        }

        public void HealthBar(short health, short maxHealth, float X, float Y, float height)
        {
            var vList = ImGui.GetForegroundDrawList();
            if (maxHealth <= 0) maxHealth = 200; // Fallback to a default max health
            float healthPercentage = (float)health / maxHealth;
            float barHeight = height * healthPercentage;

            // Set the color for the outline and the background
            uint outlineColor = ColorToUint32(Color.Black); // Black outline
            uint backgroundColor = ColorToUint32(Color.Black); // Black background

            // Set the color based on the health percentage
            uint barColor;

            if (healthPercentage > 0.8f)
            {
                // Yellow for 80% - 100%
                barColor = ColorToUint32(Color.Yellow);
            }
            else if (healthPercentage > 0.4f)
            {
                // Orange for 40% - 80%
                barColor = ColorToUint32(Color.Orange);
            }
            else
            {
                // Red for 0% - 40%
                barColor = ColorToUint32(Color.Red);
            }

            // Draw the outline of the health bar (black) with rounded corners (radius 1)
            vList.AddRect(new Vector2(X - 1, Y - 1), new Vector2(X + 5, Y + height + 1), outlineColor, 1.0f);

            // Draw the background (empty) health bar (black) with rounded corners (radius 1)
            vList.AddRectFilled(new Vector2(X, Y), new Vector2(X + 4, Y + height), backgroundColor, 1.0f);

            // Draw the filled portion of the health bar based on the health percentage with rounded corners (radius 1)
            vList.AddRectFilled(new Vector2(X, Y + (height - barHeight)), new Vector2(X + 4, Y + height), barColor, 1.0f);
        }




        public void DrawHealthBar(short health, short maxHealth, float X, float Y, float height, float width)
        {
            var vList = ImGui.GetForegroundDrawList();

            // Prevent division by zero and ensure healthPercentage is between 0 and 1
            if (maxHealth <= 0) maxHealth = 100; // Fallback to a default max health
            float healthPercentage = Math.Clamp((float)health / maxHealth, 0f, 1f);
            float healthWidth = width * healthPercentage;

            // Determine the color based on health percentage
            Color healthColor;


            if (healthPercentage < 0.3f)
            {
                healthColor = Color.FromArgb((int)(1f * 255), 255, 0, 0); // Red for health < 20%
            }
            else if (healthPercentage < 0.8f)
            {
                healthColor = Color.FromArgb((int)(1f * 255), 255, 255, 0); // Yellow for health < 70%
            }
            else
            {
                healthColor = Color.FromArgb((int)(1f * 255), 86, 255, 43); // Green for health >= 70%
            }

            // Draw the full health bar background (unfilled part)
            vList.AddRectFilled(new Vector2(X, Y - height), new Vector2(X + width, Y), ColorToUint32(Color.FromArgb((int)(1f * 255), 99, 0, 0))); // Background for health bar

            // Draw the health portion representing current health
            vList.AddRectFilled(new Vector2(X, Y - height), new Vector2(X + healthWidth, Y), ColorToUint32(healthColor)); // Health portion

            // Draw the black outline around the health bar
            vList.AddRect(new Vector2(X, Y - height), new Vector2(X + width, Y), ColorToUint32(Color.Black), 1f); // Black outline
        }
        static uint ColorToUint32(Color color)
        {
            return ImGui.ColorConvertFloat4ToU32(new Vector4(
            (float)(color.R / 255.0),
                (float)(color.G / 255.0),
                (float)(color.B / 255.0),
                (float)(color.A / 255.0)));
        }

        /// <summary>
        /// فحص صحة موقع على الشاشة
        /// </summary>
        private bool IsValidScreenPosition(Vector2 pos)
        {
            return pos.X > 0 && pos.Y > 0 &&
                   !float.IsNaN(pos.X) && !float.IsNaN(pos.Y) &&
                   !float.IsInfinity(pos.X) && !float.IsInfinity(pos.Y);
        }

        /// <summary>
        /// فحص إذا كان الموقع داخل حدود الشاشة
        /// </summary>
        private bool IsOnScreen(Vector2 pos)
        {
            return pos.X >= 0 && pos.X <= Core.Width &&
                   pos.Y >= 0 && pos.Y <= Core.Height;
        }

        /// <summary>
        /// فحص صحة الكائن
        /// </summary>
        private bool IsValidEntity(Entity entity)
        {
            if (entity == null) return false;
            if (entity.IsDead) return false;
            if (!entity.IsKnown) return false;
            if (entity.Head == Vector3.Zero) return false;
            if (entity.Root == Vector3.Zero) return false;

            return true;
        }

        /// <summary>
        /// الحصول على نقطة بداية الخط حسب الإعدادات
        /// </summary>
        private Vector2 GetLineStartPosition()
        {
            try
            {
                switch (Config.linePosition?.ToLower())
                {
                    case "up":
                        return new Vector2(Core.Width / 2f, 0f);
                    case "bottom":
                        return new Vector2(Core.Width / 2f, Core.Height);
                    case "left":
                        return new Vector2(0f, Core.Height / 2f);
                    case "right":
                        return new Vector2(Core.Width, Core.Height / 2f);
                    default:
                        return new Vector2(Core.Width / 2f, 0f); // Default to top
                }
            }
            catch
            {
                return new Vector2(Core.Width / 2f, 0f); // Fallback
            }
        }

        /// <summary>
        /// حساب نسبة الصحة
        /// </summary>
        private float CalculateHealthPercentage(short health)
        {
            try
            {
                if (health <= 0) return 0f;
                if (health > 1000) return 1f;

                float maxHealth = health > 230 ? 500f : 200f;
                return Math.Clamp((float)health / maxHealth, 0f, 1f);
            }
            catch
            {
                return 1f; // Default to full health on error
            }
        }

        /// <summary>
        /// الحصول على لون الصحة حسب النسبة
        /// </summary>
        private uint GetHealthColor(float healthPercentage)
        {
            try
            {
                if (healthPercentage > 0.8f)
                    return ColorToUint32(Color.GreenYellow);
                else if (healthPercentage > 0.4f)
                    return ColorToUint32(Color.Orange);
                else
                    return ColorToUint32(Color.Red);
            }
            catch
            {
                return ColorToUint32(Color.White); // Default color
            }
        }
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);


        const uint WDA_NONE = 0x00000000;
        const uint WDA_MONITOR = 0x00000001;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;
        void CreateHandle()
        {
            RECT rect;
            GetWindowRect(Core.Handle, out rect);
            int x = rect.Left;
            int y = rect.Top;
            int width = rect.Right - rect.Left;
            int height = rect.Bottom - rect.Top;
            ImGui.SetWindowSize(new Vector2((float)width, (float)height));
            ImGui.SetWindowPos(new Vector2((float)x, (float)y));
            Size = new Size(width, height);
            Position = new Point(x, y);

            Core.Width = width;
            Core.Height = height;
            if (Config.StreamMode)
            {
                SetWindowDisplayAffinity(hWnd, WDA_EXCLUDEFROMCAPTURE);
            }
            else
            {
                SetWindowDisplayAffinity(hWnd, WDA_NONE);
            }
        }
    }
}




