﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Client
{
    internal class AobPatchManager
    {
        private static readonly ConcurrentDictionary<string, PatchData> _patches =
            new ConcurrentDictionary<string, PatchData>();

        public class PatchData
        {
            public string PatchId { get; set; }
            public List<long> Addresses { get; set; } = new List<long>();
            public byte[] OriginalBytes { get; set; }
            public byte[] PatchedBytes { get; set; }
            public bool IsApplied { get; set; }
        }

        public static async Task InitializePatch(Flame memory, string patchId,
            string searchPattern, string replacePattern)
        {
            var searchBytes = HexStringToByteArray(searchPattern);
            var replaceBytes = HexStringToByteArray(replacePattern);

            var addresses = await memory.AoBScan(searchPattern);
            if (!addresses.Any()) return;

            foreach (var address in addresses)
            {
                byte[] currentBytes = new byte[searchBytes.Length];
                IntPtr bytesRead;

                if (Flame.ReadProcessMemory(memory._processHandle, (IntPtr)address,
                    currentBytes, (IntPtr)currentBytes.Length, out bytesRead))
                {
                    if (ByteArrayCompare(currentBytes, searchBytes, searchPattern))
                    {
                        _patches.AddOrUpdate(patchId,
                            new PatchData
                            {
                                PatchId = patchId,
                                Addresses = new List<long> { address },
                                OriginalBytes = currentBytes,
                                PatchedBytes = replaceBytes,
                                IsApplied = false
                            },
                            (key, existing) =>
                            {
                                if (!existing.Addresses.Contains(address))
                                    existing.Addresses.Add(address);
                                return existing;
                            });
                    }
                }
            }
        }

        public static bool TogglePatch(Flame memory, string patchId, bool activate)
        {
            if (!_patches.TryGetValue(patchId, out var patch))
                return false;

            bool success = true;
            foreach (var address in patch.Addresses)
            {
                var bytesToWrite = activate ? patch.PatchedBytes : patch.OriginalBytes;
                if (!Flame.WriteProcessMemory(memory._processHandle, (IntPtr)address,
                    bytesToWrite, (IntPtr)bytesToWrite.Length, IntPtr.Zero))
                {
                    success = false;
                }
            }

            if (success)
                patch.IsApplied = activate;

            return success;
        }

        public static bool IsPatchApplied(string patchId)
        {
            return _patches.TryGetValue(patchId, out var patch) && patch.IsApplied;
        }

        private static byte[] HexStringToByteArray(string hex)
        {
            return hex.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries)
                     .Select(s => s == "??" ? (byte)0 : Convert.ToByte(s, 16))
                     .ToArray();
        }

        private static bool ByteArrayCompare(byte[] bytes, byte[] patternBytes, string pattern)
        {
            var patternParts = pattern.Split(' ');
            for (int i = 0; i < patternParts.Length; i++)
            {
                if (patternParts[i] != "??" && bytes[i] != patternBytes[i])
                    return false;
            }
            return true;
        }
    }
}

